<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SCANNER LOG [FILTER MODE]</title>
    <style>
        /* Unified font style for the entire document */
        body, label {
            font-family: Arial, sans-serif;
            font-size: 14px;
        }

        /* Container for the search and refresh controls */
        #controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            width: 100%;
            box-sizing: border-box;
        }

        /* Flexible area for the search input */
        #searchContainer {
            flex: 1;
            display: flex;
            align-items: center;
        }

        /* Styling for the search input */
        #searchInput {
            padding: 5px;
            width: 100%;
            max-width: 300px;
            min-width: 100px; /* Ensures a minimum width */
            box-sizing: border-box;
            margin-right: 10px;
        }

        /* Styling for the custom distance input */
        #freeDistanceInput {
            height: 14px; 
            width: 50px; 
        }
        
        .distance-label {
            position: relative;
            top: 3px; /* Moves the 'km' label 5 pixels down */
        }

        /* Container for the distance filter checkboxes */
        #filterContainer {
            display: flex;
            gap: 10px;
        }

        /* Container for the buttons */
        #buttonContainer {
            display: flex;
            align-items: center;
        }

        /* Styling for the buttons */
        .button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            color: white;
            margin-left: 10px; /* Space between buttons */
        }

        /* Styling for the refresh button */
        #refreshButton {
            background-color: #007bff;
        }

        /* Styling for the dark mode button */
        #darkModeButton {
            background-color: #6c757d;
        }

        /* Styling for the refresh button when auto-refresh is off */
        #refreshButton.off {
            background-color: #6c757d;
        }

        /* Dark mode styling */
        body.dark-mode {
            background-color: #121212;
            color: white;
        }

        body.dark-mode table {
            color: white;
        }

        body.dark-mode th {
            background-color: #1f1f1f;
        }

        body.dark-mode td {
            border-color: #333;
        }

        /* Make table headers clickable */
        th {
            cursor: pointer;
        }

        /* Table styling */
        table {
            width: 100%;
            border-collapse: collapse;
            table-layout: auto; /* Ensure columns are auto-sized */
        }

        /* Styling for table cells */
        th, td {
            padding: 3px;
            text-align: left;
            white-space: nowrap; /* Prevents text from wrapping */
        }

        /* Background color for table headers */
        th {
            background-color: #f2f2f2;
        }

        /* Dark mode styling for links */
        body.dark-mode a {
            color: #d6a8f5; /* Light purple color */
        }

        body.dark-mode a:hover {
            color: #b57edc; /* Slightly darker purple for hover effect */
        }

        /* Dark mode styling for the search input */
        body.dark-mode #searchInput {
            background-color: #6c757d; /* Match the color of the dark mode toggle button */
            color: white; /* Ensure text is readable */
        }

        /* Dark mode styling for the search input placeholder */
        body.dark-mode #searchInput::placeholder {
            color: white; /* Light gray placeholder text for better readability */
        }
    </style>
<script>
    let autoRefresh = true; // Controls whether auto-refresh is enabled
    let refreshInterval = 10000; // Refresh interval in milliseconds (10 seconds)
    let refreshTimeout; // Timeout ID for the refresh function
    let baseUrl = ""; // Variable to store the dynamic base URL

    /* Get the value of a cookie by name */
    function getCookie(name) {
        const cookieName = name + "=";
        const cookies = decodeURIComponent(document.cookie).split(';');
        for (let i = 0; i < cookies.length; i++) {
            let cookie = cookies[i];
            while (cookie.charAt(0) === ' ') {
                cookie = cookie.substring(1);
            }
            if (cookie.indexOf(cookieName) === 0) {
                return cookie.substring(cookieName.length, cookie.length);
            }
        }
        return "";
    }

    /* Set a cookie with a specific name, value, and expiration (in days) */
    function setCookie(name, value, days) {
        const expires = "expires=" + new Date(Date.now() + days * 864e5).toUTCString();
        document.cookie = name + "=" + value + ";" + expires + ";path=/";
    }

    /* Toggle the auto-refresh functionality */
    function toggleRefresh() {
        autoRefresh = !autoRefresh;
        const button = document.getElementById('refreshButton');
        button.classList.toggle('off', !autoRefresh);
        if (autoRefresh) {
            startAutoRefresh();
        } else {
            clearTimeout(refreshTimeout);
        }
    }

    /* Starts the auto-refresh function */
    function startAutoRefresh() {
        refreshTimeout = setTimeout(() => {
            location.reload();
        }, refreshInterval);
    }

    /* Extracts the base URL from the first entry */
	function extractBaseUrl() {
		// Get all anchor elements within the table
		const links = document.querySelectorAll("table tr td a");
    
		// Loop through each link
		for (let i = 0; i < links.length; i++) {
			const href = links[i].href;

			// Check if the href contains "https://maps.fmdx.org"
			if (href.includes("https://maps.fmdx.org")) {
				// Extract the base part up to '&id='
				const splitLink = href.split('&id=')[0];
				// Add the '&id=' back to form the correct base URL
				baseUrl = splitLink + "&id=";
				break; // Exit the loop once we find the first matching link
			}
		}
	}

    /* Sorts the table based on column index (n) */
    function sortTable(n, isNumeric = false) {
        const table = document.querySelector("table");
        let rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
        switching = true;
        dir = "asc";

        /* Keep switching rows until sorting is complete */
        while (switching) {
            switching = false;
            rows = table.rows;

            /* Loop through the rows, skipping the header */
            for (i = 1; i < (rows.length - 1); i++) {
                shouldSwitch = false;
                x = rows[i].getElementsByTagName("TD")[n];
                y = rows[i + 1].getElementsByTagName("TD")[n];

                /* Compare based on numeric or string content */
                let xContent = isNumeric ? parseFloat(x.innerHTML) || 0 : x.innerHTML.toLowerCase();
                let yContent = isNumeric ? parseFloat(y.innerHTML) || 0 : y.innerHTML.toLowerCase();

                /* Determine if a switch is needed based on direction */
                if (dir == "asc") {
                    if (xContent > yContent) {
                        shouldSwitch = true;
                        break;
                    }
                } else if (dir == "desc") {
                    if (xContent < yContent) {
                        shouldSwitch = true;
                        break;
                    }
                }
            }
            if (shouldSwitch) {
                /* Make the switch and mark switching as true */
                rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
                switching = true;
                switchcount++;
            } else {
                /* If no switching happened, change the direction */
                if (switchcount == 0 && dir == "asc") {
                    dir = "desc";
                    switching = true;
                }
            }
        }
    }

/* Filter table based on user input and distance */
function filterTable() {
    const input = document.getElementById("searchInput");
    const filter = input.value.toLowerCase();
    const table = document.querySelector("table");
    const tr = table.getElementsByTagName("tr");

    // Get the PI codes from the new input field
    const piCodesInput = document.getElementById("piCodesInput").value;
    const piCodesArray = piCodesInput.split(',')
        .map(code => code.trim().replace(/\?$/, '')) // Remove trailing question marks
        .filter(code => code); // Trim and filter empty codes

    // Checkbox filter
    const filter150 = document.getElementById("filter150").checked;
    const filter300 = document.getElementById("filter300").checked;
    const filter700 = document.getElementById("filter700").checked;
    const filter1300 = document.getElementById("filter1300").checked;
    const filterFreeDistance = document.getElementById("filterFreeDistance").checked;
    const freeDistanceInput = parseFloat(document.getElementById("freeDistanceInput").value);

    let distanceFilter = null; // Start with no distance filter

    if (filter150) {
        distanceFilter = 150;
    } else if (filter300) {
        distanceFilter = 300;
    } else if (filter700) {
        distanceFilter = 700;
    } else if (filter1300) {
        distanceFilter = 1300;
    } else if (filterFreeDistance && !isNaN(freeDistanceInput)) {
        distanceFilter = freeDistanceInput; // Use custom distance if checkbox is checked and value is valid
    }

    // Loop through all table rows
    for (let i = 1; i < tr.length; i++) {
        let td = tr[i].getElementsByTagName("td");
        let display = true; // Start assuming the row should be displayed

        // Filter based on search input
        if (filter) {
            let rowMatches = false; // Assume no match initially
            for (let j = 0; j < td.length; j++) {
                if (td[j].textContent.toLowerCase().indexOf(filter) > -1) {
                    rowMatches = true; // Set match to true if any cell matches
                    break;
                }
            }
            if (!rowMatches) {
                display = false; // If no cells matched the filter, hide the row
            }
        }

        // Filter based on distance
        if (display && distanceFilter !== null) {
            let distValue = parseFloat(td[12].textContent) || 0; // Assume 'DIST' is in the 13th column (index 12)
            if (distValue < distanceFilter) {
                display = false; // Hide row if distance is less than the filter value
            }
        }

        // Filter based on PI codes
        if (display && piCodesArray.length > 0) {
            const piCodeValue = td[3].textContent.trim().replace(/\?$/, ''); // Remove trailing question mark
            if (piCodesArray.includes(piCodeValue)) {
                display = false; // Hide row if PI code is in the exclusion list
            }
        }

        // Show or hide the row based on the filters
        tr[i].style.display = display ? "" : "none";
    }

    generateDynamicLink(); // Update the link after filtering
}

    /* Toggle Dark Mode */
    function toggleDarkMode() {
        const body = document.body;
        const isDarkMode = body.classList.toggle("dark-mode");
        setCookie("darkMode", isDarkMode, 365); // Save preference for 1 year
    }

    /* Apply Dark Mode based on cookie */
    function applyDarkMode() {
        const darkMode = getCookie("darkMode");
        if (darkMode === "true") {
            document.body.classList.add("dark-mode");
        }
    }

    /* Generates a dynamic link based on visible table rows */
	function generateDynamicLink() {
		const table = document.querySelector("table");
		const rows = table.getElementsByTagName("tr");
		const uniqueIds = new Set();

		for (let i = 1; i < rows.length; i++) {
			if (rows[i].style.display !== "none") {
				const cells = rows[i].getElementsByTagName("td");
				const idCell = cells[cells.length - 5];
				if (idCell && idCell.textContent.trim()) {
					uniqueIds.add(idCell.textContent.trim());
				}
			}
		}

		if (uniqueIds.size > 0) {
			const idsString = Array.from(uniqueIds).join(",");
			const dynamicLink = baseUrl + idsString + "&findId=*";

			let container = document.getElementById("dynamicLinkContainer");
			if (!container) {
				container = document.createElement("div");
				container.id = "dynamicLinkContainer";
				// Container vor der Tabelle einfügen
				table.parentNode.insertBefore(container, table);
			}
			// Inhalt zurücksetzen
			container.innerHTML = "";

			// Link erstellen
			const link = document.createElement("a");
			link.target = "_blank";
			link.href = dynamicLink;
			link.textContent = "MAP ALL";
			container.appendChild(link);

			// Abstand zum Table
			container.style.marginBottom = "1em";  // <-- hier den Abstand einstellen
		}
	}

    /* Initializes controls and auto-refresh on DOMContentLoaded */
    document.addEventListener('DOMContentLoaded', () => {
        applyDarkMode(); // Apply Dark Mode preference
        extractBaseUrl(); // Extract base URL from the first entry

        const controlsContainer = document.createElement('div');
        controlsContainer.id = 'controls';

        /* Create and append the search input container */
        const searchContainer = document.createElement('div');
        searchContainer.id = 'searchContainer';

        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.id = 'searchInput';
        searchInput.placeholder = 'Search table ...';
        searchInput.onkeyup = filterTable;
        searchContainer.appendChild(searchInput);

        /* Create and append the distance filter container */
        const filterContainer = document.createElement('div');
        filterContainer.id = 'filterContainer';

        filterContainer.innerHTML = `
            <label><input type="checkbox" id="filter150" onchange="filterTable()"> ≥ 150 km</label>
            <label><input type="checkbox" id="filter300" onchange="filterTable()"> ≥ 300 km</label>
            <label><input type="checkbox" id="filter700" onchange="filterTable()"> ≥ 700 km</label>
            <label><input type="checkbox" id="filter1300" onchange="filterTable()"> ≥ 1300 km</label>

            <label>
                <input type="checkbox" id="filterFreeDistance" onchange="filterTable()"> Custom:
            </label>
            <input type="number" id="freeDistanceInput" placeholder="" min="0" oninput="filterTable()">
            <span class="distance-label">km</span>

			<!-- New Input Field for PI Codes -->
			<label for="piCodesInput" style="margin-top: 3px; display: block;">Exclude PI Codes (comma separated):</label>
			<input type="text" id="piCodesInput" placeholder="e.g. 6201,6202,6203" oninput="filterTable()">
        `;

        searchContainer.appendChild(filterContainer);
        controlsContainer.appendChild(searchContainer);

        /* Create and append the button container */
        const buttonContainer = document.createElement('div');
        buttonContainer.id = 'buttonContainer';

        /* Create and append the dark mode button */
        const darkModeButton = document.createElement('button');
        darkModeButton.id = 'darkModeButton';
        darkModeButton.className = 'button';
        darkModeButton.innerText = 'Toggle Dark Mode';
        darkModeButton.addEventListener('click', toggleDarkMode);
        buttonContainer.appendChild(darkModeButton);

        /* Create and append the refresh button */
        const refreshButton = document.createElement('button');
        refreshButton.id = 'refreshButton';
        refreshButton.className = 'button';
        refreshButton.innerText = 'Auto Refresh: ON';
        refreshButton.addEventListener('click', () => {
            toggleRefresh();
            refreshButton.innerText = `Auto Refresh: ${autoRefresh ? 'ON' : 'OFF'}`;
        });
        buttonContainer.appendChild(refreshButton);

        /* Append the button container to the controls container */
        controlsContainer.appendChild(buttonContainer);

        /* Insert controls above the table */
        document.body.insertBefore(controlsContainer, document.querySelector("pre"));

        /* Start auto-refresh if enabled */
        if (autoRefresh) {
            startAutoRefresh();
        }

        /* Add click event listeners to table headers for sorting */
		const headers = document.querySelectorAll("th");
		headers[1].addEventListener("click", () => sortTable(1));           // TIME(UTC)
		headers[2].addEventListener("click", () => sortTable(2, true));     // FREQ
		headers[3].addEventListener("click", () => sortTable(3));           // PI
		headers[4].addEventListener("click", () => sortTable(4));           // PS
		headers[5].addEventListener("click", () => sortTable(5));           // NAME
		headers[6].addEventListener("click", () => sortTable(6));           // CITY
		headers[7].addEventListener("click", () => sortTable(7));           // ITU
		headers[8].addEventListener("click", () => sortTable(8));           // ANT
		headers[9].addEventListener("click", () => sortTable(9));           // P
		headers[10].addEventListener("click", () => sortTable(10, true));   // ERP(kW)
		headers[11].addEventListener("click", () => sortTable(11, true));   // STRENGTH(dBµV)
		headers[12].addEventListener("click", () => sortTable(12, true));   // DIST(km)
		headers[13].addEventListener("click", () => sortTable(13, true));   // AZ(°)
		headers[14].addEventListener("click", () => sortTable(14));         // ID

        // Event listeners to ensure only one checkbox is selected
        document.querySelectorAll('#filterContainer input[type="checkbox"]').forEach((checkbox) => {
            checkbox.addEventListener('change', function () {
                if (this.checked) {
                    document.querySelectorAll('#filterContainer input[type="checkbox"]').forEach((cb) => {
                        if (cb !== this) cb.checked = false;
                    });
                }
                filterTable(); // Update the table after changing a checkbox
            });
        });

        generateDynamicLink();
    });
</script>
</head>
<body>
<pre><br><br>SCANNER LOG (FILTER MODE) 2025-07-13 05:21:59 (UTC)<br><br><table border="1"><tr><th>DATE</th><th>TIME(UTC)</th><th>FREQ</th><th>PI</th><th>PS</th><th>NAME</th><th>CITY</th><th>ITU</th><th>ANT</th><th>P</th><th>ERP(kW)</th><th>STRENGTH(dBf)</th><th>DIST(km)</th><th>AZ(°)</th><th>ID</th><th>MODE</th><th>STREAM</th><th>MAP</th><th>FMLIST</th></tr>
<tr><td>2025-07-13</td><td>05:21:59</td><td>91.500</td><td>3915</td><td>__ooth__?</td><td>Smooth FM</td><td>Melbourne</td><td>AUS</td><td>Ant A</td><td>M</td><td>56</td><td>24.0</td><td>34</td><td>93</td><td>37700213</td><td>A</td><td><a href="#" onclick="window.open('https://fmscan.org/stream.php?i=37700213', 'newWindow', 'width=800,height=160'); return false;" target="_blank">STREAM</a></td><td><a href="https://maps.fmdx.org/#qth=-37.814124,144.964508&id=37700213&findId=*" target="_blank">MAP</a></td><td><a href="https://www.fmlist.org/fi_inslog.php?lfd=37700213&qrb=34&qtf=93&country=AUS&omid=10342" target="_blank">FMLIST</a></td></tr>
<tr><td>2025-07-13</td><td>05:22:06</td><td>93.100</td><td>3931</td><td>SBS_____</td><td>SBS Radio 2</td><td>Melbourne</td><td>AUS</td><td>Ant A</td><td>M</td><td>100</td><td>19.9</td><td>34</td><td>94</td><td>37700346</td><td>A</td><td><a href="#" onclick="window.open('https://fmscan.org/stream.php?i=37700346', 'newWindow', 'width=800,height=160'); return false;" target="_blank">STREAM</a></td><td><a href="https://maps.fmdx.org/#qth=-37.814124,144.964508&id=37700346&findId=*" target="_blank">MAP</a></td><td><a href="https://www.fmlist.org/fi_inslog.php?lfd=37700346&qrb=34&qtf=94&country=AUS&omid=10342" target="_blank">FMLIST</a></td></tr>
</pre></body></html>