/**
 * Plugin Management JavaScript
 * Handles hot reloading of server-side plugins
 */

let pluginManagementData = {};
let pluginStatusInterval = null;
let pluginsNeedRefresh = false;

$(document).ready(function() {
    // Load plugin management when the panel is shown
    $('[data-panel="plugin-management"]').on('click', function() {
        setTimeout(loadPluginManagement, 100);
    });

    // Check if we're already on the plugin management panel (direct hash load)
    if (window.location.hash === '#plugin-management') {
        setTimeout(loadPluginManagement, 200);
    }

    // Check for plugins tab refresh when switching to it
    $('[data-panel="plugins-tab"]').on('click', function() {
        setTimeout(checkPluginsTabRefresh, 100);
    });

    // Auto-refresh plugin status every 10 seconds when panel is visible
    setInterval(function() {
        if ($('#plugin-management').is(':visible') && pluginManagementData && Array.isArray(pluginManagementData) && pluginManagementData.length > 0) {
            refreshPluginStatus();
        }
    }, 10000);
});

/**
 * Load plugin management interface
 */
function loadPluginManagement() {
    if ($('#plugin-management').is(':visible')) {
        refreshPluginList();
    }
}

/**
 * Refresh the plugin list
 */
function refreshPluginList(retryCount = 0) {
    const container = $('#plugin-management-container');
    container.html('<div class="loading-indicator"><i class="fa-solid fa-spinner fa-spin"></i> Loading plugins...</div>');

    $.ajax({
        url: '/api/plugins',
        method: 'GET',
        timeout: 10000, // 10 second timeout
        success: function(response) {
            console.log('Plugin API response:', response);
            if (response.success) {
                pluginManagementData = response.plugins;
                renderPluginList(response.plugins);
            } else {
                console.error('Plugin API returned success=false:', response);
                showPluginError('Failed to load plugins: ' + (response.error || 'Unknown error'));
            }
        },
        error: function(xhr, status, error) {
            console.error('Plugin API error:', xhr.status, xhr.responseText, 'retry:', retryCount);

            // If it's a 403 and we haven't retried yet, wait and try again (auth might be in progress)
            if (xhr.status === 403 && retryCount < 2) {
                setTimeout(() => {
                    refreshPluginList(retryCount + 1);
                }, 1000);
                return;
            }

            let errorMessage = 'Failed to load plugins';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage += ': ' + xhr.responseJSON.error;
            } else if (xhr.status === 403) {
                errorMessage += ': Unauthorized - please log in as admin';
            } else if (status === 'timeout') {
                errorMessage += ': Request timed out';
            }
            showPluginError(errorMessage);
        }
    });
}

/**
 * Refresh plugin status without reloading the entire list
 */
function refreshPluginStatus() {
    if (!pluginManagementData || !Array.isArray(pluginManagementData) || pluginManagementData.length === 0) {
        return;
    }

    $.ajax({
        url: '/api/plugins',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                updatePluginStatuses(response.plugins);
            }
        },
        error: function() {
            // Silently fail for status updates
        }
    });
}

/**
 * Update plugin statuses in the UI
 */
function updatePluginStatuses(plugins) {
    plugins.forEach(function(plugin) {
        const pluginCard = $(`[data-plugin-name="${plugin.name}"]`);
        if (pluginCard.length > 0) {
            updatePluginCard(pluginCard, plugin);
        }
    });
}

/**
 * Render the plugin list
 */
function renderPluginList(plugins) {
    const container = $('#plugin-management-container');
    
    if (plugins.length === 0) {
        container.html('<div class="no-plugins"><i class="fa-solid fa-info-circle"></i> No server-side plugins found.</div>');
        return;
    }
    
    let html = '<div class="plugin-grid">';
    
    plugins.forEach(function(plugin) {
        html += createPluginCard(plugin);
    });
    
    html += '</div>';
    container.html(html);
}

/**
 * Create a plugin card HTML
 */
function createPluginCard(plugin) {
    const statusClass = getStatusClass(plugin.status.status);
    const statusText = getStatusText(plugin.status.status);
    const isLoaded = plugin.status.status === 'loaded';
    const displayName = plugin.displayName || plugin.name;

    // Check if plugin is hot reload compatible
    console.log(`Checking compatibility for plugin: ${plugin.name}`, plugin);
    const isHotReloadCompatible = isPluginHotReloadCompatible(plugin.name);
    console.log(`Plugin ${plugin.name} compatibility result: ${isHotReloadCompatible}`);

    return `
        <div class="plugin-card" data-plugin-name="${plugin.name}">
            <div class="plugin-column-1">
                <h4 class="plugin-name">${displayName}</h4>
                <div class="plugin-version">Version: <strong>${plugin.version || plugin.status?.version || 'Unknown'}</strong></div>
                <div class="plugin-status">Status: <strong>${statusText}</strong></div>
            </div>

            <div class="plugin-column-status">
                <span class="status-indicator ${statusClass}" title="${statusText}"></span>
            </div>

            ${isLoaded ? `
                <div class="plugin-column-2">
                    <div class="plugin-load-time">Load Time: ${new Date(plugin.status.loadTime).toLocaleString()}</div>
                    <div class="plugin-stats">Timers: ${plugin.status.timersCount} | WebSockets: ${plugin.status.webSocketsCount} | Cleanup Handlers: ${plugin.status.cleanupHandlersCount}</div>
                </div>
            ` : `
                <div class="plugin-column-2">
                    <div class="plugin-not-loaded">Plugin not loaded</div>
                </div>
            `}

            <div class="plugin-actions">
                ${isLoaded ? `
                    ${isHotReloadCompatible ? `
                        <button class="btn btn-warning btn-sm" onclick="restartPlugin('${plugin.name}')"
                                title="Restart this plugin">
                            <i class="fa-solid fa-refresh"></i> Restart
                        </button>
                    ` : `
                        <button class="btn btn-secondary btn-sm btn-disabled" disabled
                                title="Plugin not compatible with hot reload - see HOT_RELOAD_GUIDE.md">
                            <i class="fa-solid fa-refresh"></i> Restart
                        </button>
                        <button class="btn btn-warning btn-sm" onclick="testRestartPlugin('${plugin.name}')"
                                title="Test hot reload (may cause issues - use for development only)">
                            <i class="fa-solid fa-flask"></i> Test Restart
                        </button>
                    `}
                ` : `
                    <button class="btn btn-success btn-sm" onclick="loadPlugin('${plugin.name}')"
                            title="Load this plugin">
                        <i class="fa-solid fa-play"></i> Load
                    </button>
                `}
            </div>
        </div>
    `;
}

/**
 * Update a plugin card with new data
 */
function updatePluginCard(pluginCard, plugin) {
    const statusIndicator = pluginCard.find('.status-indicator');
    const statusClass = getStatusClass(plugin.status.status);
    const statusText = getStatusText(plugin.status.status);
    
    // Update status indicator
    statusIndicator.removeClass('status-loaded status-unloaded status-restarting status-error')
                   .addClass(statusClass)
                   .attr('title', statusText);
    
    // Update status text
    pluginCard.find('.plugin-status').html(`Status: <strong>${statusText}</strong>`);

    // Update version
    const newVersion = plugin.version || plugin.status?.version || 'Unknown';
    const versionDiv = pluginCard.find('.plugin-version');
    if (versionDiv.length > 0) {
        versionDiv.html(`Version: <strong>${newVersion}</strong>`);
    } else {
        pluginCard.find('.plugin-status').after(`<div class="plugin-version">Version: <strong>${newVersion}</strong></div>`);
    }

    // Update plugin details and actions
    const isLoaded = plugin.status.status === 'loaded';
    const infoDiv = pluginCard.find('.plugin-info');
    const actionsDiv = pluginCard.find('.plugin-actions');
    
    if (isLoaded) {
        // Update the existing plugin-stats structure (matches initial card creation)
        const statsDiv = pluginCard.find('.plugin-stats');
        const loadTimeDiv = pluginCard.find('.plugin-load-time');

        if (statsDiv.length > 0) {
            statsDiv.html(`Timers: ${plugin.status.timersCount} | WebSockets: ${plugin.status.webSocketsCount} | Cleanup Handlers: ${plugin.status.cleanupHandlersCount}`);
        }

        if (loadTimeDiv.length > 0) {
            loadTimeDiv.html(`Load Time: ${new Date(plugin.status.loadTime).toLocaleString()}`);
        }
        
        // Update actions for loaded plugin
        actionsDiv.html(`
            ${isPluginHotReloadCompatible(plugin.name) ? `
                <button class="btn btn-warning btn-sm" onclick="restartPlugin('${plugin.name}')"
                        title="Restart this plugin">
                    <i class="fa-solid fa-refresh"></i> Restart
                </button>
            ` : `
                <button class="btn btn-secondary btn-sm btn-disabled" disabled
                        title="Plugin not compatible with hot reload - see HOT_RELOAD_GUIDE.md">
                    <i class="fa-solid fa-refresh"></i> Restart
                </button>
                <button class="btn btn-warning btn-sm" onclick="testRestartPlugin('${plugin.name}')"
                        title="Test hot reload (may cause issues - use for development only)">
                    <i class="fa-solid fa-flask"></i> Test Restart
                </button>
            `}
        `);
    } else {
        // Remove details for unloaded plugin
        infoDiv.find('.plugin-details').remove();
        
        // Update actions for unloaded plugin
        actionsDiv.html(`
            <button class="btn btn-success btn-sm" onclick="loadPlugin('${plugin.name}')" 
                    title="Load this plugin">
                <i class="fa-solid fa-play"></i> Load
            </button>
        `);
    }
}

/**
 * Get CSS class for plugin status
 */
function getStatusClass(status) {
    switch (status) {
        case 'loaded': return 'status-loaded';
        case 'unloaded': return 'status-unloaded';
        case 'restarting': return 'status-restarting';
        case 'error': return 'status-error';
        default: return 'status-unloaded';
    }
}

/**
 * Get human-readable status text
 */
function getStatusText(status) {
    switch (status) {
        case 'loaded': return 'Loaded';
        case 'unloaded': return 'Unloaded';
        case 'restarting': return 'Restarting';
        case 'error': return 'Error';
        default: return 'Unknown';
    }
}

/**
 * Check if a plugin is compatible with hot reload
 */
function isPluginHotReloadCompatible(pluginName) {
    // Find the plugin in the current data (pluginManagementData is an array)
    const plugin = pluginManagementData?.find(p => p.name === pluginName);
    if (!plugin || !plugin.status || plugin.status.status !== 'loaded') {
        return false; // Unloaded plugins can't be restarted
    }

    // Plugins with cleanup handlers are considered hot reload compatible
    // This indicates they have proper hot reload support implemented
    return plugin.status.cleanupHandlersCount > 0;
}

/**
 * Check if plugins tab needs refresh and update it
 */
function checkPluginsTabRefresh() {
    if (pluginsNeedRefresh && $('#plugins-tab').is(':visible')) {
        refreshPluginsTabVersions();
        pluginsNeedRefresh = false;
    }
}

/**
 * Refresh plugin versions in the plugins tab dropdown
 */
function refreshPluginsTabVersions() {
    $.ajax({
        url: '/api/plugin-configs-refresh',
        method: 'GET',
        success: function(response) {
            if (response.success && response.plugins) {
                updatePluginsTabDropdown(response.plugins);
            }
        },
        error: function() {
            // Silently fail
        }
    });
}

/**
 * Update the plugins tab dropdown with refreshed plugin configs
 */
function updatePluginsTabDropdown(plugins) {
    const pluginsSelect = $('#plugins');
    if (pluginsSelect.length === 0) return;

    // Get currently selected plugins
    const selectedPlugins = [];
    pluginsSelect.find('option:selected').each(function() {
        selectedPlugins.push($(this).attr('data-name'));
    });

    // Clear and rebuild options
    pluginsSelect.empty();

    plugins.forEach(function(plugin) {
        const isSelected = selectedPlugins.includes(plugin.frontEndPath);
        const option = $(`<option data-name="${plugin.frontEndPath}" title="${plugin.name} by ${plugin.author} [v${plugin.version}]" ${isSelected ? 'selected' : ''}>
            ${plugin.name} by ${plugin.author} [v${plugin.version}]
        </option>`);
        pluginsSelect.append(option);
    });

    // Refresh the multiselect display
    if (pluginsSelect.hasClass('multiselect')) {
        try {
            pluginsSelect.multiselect('refresh');
        } catch (e) {
            // Multiselect might not be initialized yet
        }
    }
}



/**
 * Show error message in plugin container
 */
function showPluginError(message) {
    const container = $('#plugin-management-container');
    container.html(`<div class="plugin-error"><i class="fa-solid fa-exclamation-triangle"></i> ${message}</div>`);
}

/**
 * Set plugin status to restarting
 */
function setPluginRestarting(pluginName) {
    const pluginCard = $(`[data-plugin-name="${pluginName}"]`);
    if (pluginCard.length > 0) {
        const statusIndicator = pluginCard.find('.status-indicator');
        statusIndicator.removeClass('status-loaded status-unloaded status-error')
                       .addClass('status-restarting')
                       .attr('title', 'Restarting');
        
        pluginCard.find('.plugin-status').html('Status: <strong>Restarting</strong>');
        
        // Disable action buttons
        pluginCard.find('.plugin-actions button').prop('disabled', true);
    }
}

/**
 * Restart a plugin
 */
function restartPlugin(pluginName) {
    if (!confirm(`Are you sure you want to restart the plugin "${pluginName}"?`)) {
        return;
    }

    setPluginRestarting(pluginName);

    $.ajax({
        url: `/api/plugins/${pluginName}/restart`,
        method: 'POST',
        success: function(response) {
            if (response.success) {
                sendToast('success', 'Plugin Restarted', `Plugin "${pluginName}" has been restarted successfully.`);
                pluginsNeedRefresh = true;
                setTimeout(refreshPluginList, 1000);
            } else {
                sendToast('error', 'Restart Failed', response.error || 'Failed to restart plugin');
                setTimeout(refreshPluginList, 1000);
            }
        },
        error: function(xhr, status, error) {
            let errorMessage = 'Failed to restart plugin';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            }
            sendToast('error', 'Restart Failed', errorMessage);
            setTimeout(refreshPluginList, 1000);
        }
    });
}

/**
 * Test restart a plugin (for development/testing of incompatible plugins)
 */
function testRestartPlugin(pluginName) {
    const warningMessage = `⚠️ WARNING: "${pluginName}" is not fully compatible with hot reload.\n\n` +
                          `This may cause:\n` +
                          `• Hundreds of timers to be created\n` +
                          `• Plugin functionality to break\n` +
                          `• Need to restart the entire server\n\n` +
                          `This is for TESTING ONLY. Continue?`;

    if (!confirm(warningMessage)) {
        return;
    }

    setPluginRestarting(pluginName);

    $.ajax({
        url: `/api/plugins/${pluginName}/restart`,
        method: 'POST',
        success: function(response) {
            if (response.success) {
                sendToast('warning', 'Plugin Test Restarted', `Plugin "${pluginName}" test restart completed. Monitor server logs for issues.`);
                pluginsNeedRefresh = true;
                setTimeout(refreshPluginList, 1000);
            } else {
                sendToast('error', 'Test Restart Failed', response.error || 'Failed to test restart plugin');
                setTimeout(refreshPluginList, 1000);
            }
        },
        error: function(xhr, status, error) {
            let errorMessage = 'Failed to test restart plugin';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            }
            sendToast('error', 'Test Restart Failed', errorMessage);
            setTimeout(refreshPluginList, 1000);
        }
    });
}

/**
 * Load a plugin
 */
function loadPlugin(pluginName) {
    if (!confirm(`Are you sure you want to load the plugin "${pluginName}"?`)) {
        return;
    }

    setPluginRestarting(pluginName);

    $.ajax({
        url: `/api/plugins/${pluginName}/load`,
        method: 'POST',
        success: function(response) {
            if (response.success) {
                sendToast('success', 'Plugin Loaded', `Plugin "${pluginName}" has been loaded successfully.`);
                setTimeout(refreshPluginList, 1000);
            } else {
                sendToast('error', 'Load Failed', response.error || 'Failed to load plugin');
                setTimeout(refreshPluginList, 1000);
            }
        },
        error: function(xhr, status, error) {
            let errorMessage = 'Failed to load plugin';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            }
            sendToast('error', 'Load Failed', errorMessage);
            setTimeout(refreshPluginList, 1000);
        }
    });
}


