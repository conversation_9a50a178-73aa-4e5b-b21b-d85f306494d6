const fs = require('fs');
const path = require('path');
const { logInfo, logWarn, logError } = require('./console');

class PluginManager {
    constructor() {
        this.loadedPlugins = new Map(); // pluginName -> plugin instance
        this.pluginCleanupHandlers = new Map(); // pluginName -> cleanup functions
        this.pluginTimers = new Map(); // pluginName -> Set of timer IDs
        this.pluginWebSockets = new Map(); // pluginName -> Set of WebSocket instances
        this.pluginEventListeners = new Map(); // pluginName -> Set of event listener info
        this.pluginModuleCache = new Map(); // pluginName -> module cache keys
        this.currentPlugin = null; // Track which plugin is currently being loaded
    }

    /**
     * Create a wrapped context for plugin execution with resource tracking
     */
    createPluginContext(pluginName) {
        const originalSetTimeout = global.setTimeout;
        const originalSetInterval = global.setInterval;
        const originalClearTimeout = global.clearTimeout;
        const originalClearInterval = global.clearInterval;

        // Initialize tracking sets for this plugin
        if (!this.pluginTimers.has(pluginName)) {
            this.pluginTimers.set(pluginName, new Set());
        }
        if (!this.pluginWebSockets.has(pluginName)) {
            this.pluginWebSockets.set(pluginName, new Set());
        }
        if (!this.pluginEventListeners.has(pluginName)) {
            this.pluginEventListeners.set(pluginName, new Set());
        }

        const timers = this.pluginTimers.get(pluginName);
        const webSockets = this.pluginWebSockets.get(pluginName);
        const eventListeners = this.pluginEventListeners.get(pluginName);

        // Wrapped timer functions that track created timers
        const wrappedSetTimeout = (callback, delay, ...args) => {
            const timerId = originalSetTimeout((...callbackArgs) => {
                timers.delete(timerId);
                callback(...callbackArgs);
            }, delay, ...args);
            timers.add(timerId);
            return timerId;
        };

        const wrappedSetInterval = (callback, delay, ...args) => {
            const timerId = originalSetInterval(callback, delay, ...args);
            timers.add(timerId);
            return timerId;
        };

        const wrappedClearTimeout = (timerId) => {
            timers.delete(timerId);
            return originalClearTimeout(timerId);
        };

        const wrappedClearInterval = (timerId) => {
            timers.delete(timerId);
            return originalClearInterval(timerId);
        };

        // WebSocket wrapper to track connections
        const WebSocketWrapper = class extends require('ws') {
            constructor(...args) {
                super(...args);
                webSockets.add(this);

                // Clean up when WebSocket closes
                this.on('close', () => {
                    webSockets.delete(this);
                });
            }
        };

        return {
            setTimeout: wrappedSetTimeout,
            setInterval: wrappedSetInterval,
            clearTimeout: wrappedClearTimeout,
            clearInterval: wrappedClearInterval,
            WebSocket: WebSocketWrapper,
            // Provide a cleanup registration function for plugins
            registerCleanup: (cleanupFn) => {
                if (!this.pluginCleanupHandlers.has(pluginName)) {
                    this.pluginCleanupHandlers.set(pluginName, []);
                }
                this.pluginCleanupHandlers.get(pluginName).push(cleanupFn);
                logInfo(`Plugin Manager: Cleanup handler registered for ${pluginName}, total: ${this.pluginCleanupHandlers.get(pluginName).length}`);
            },
            // Provide access to original functions if needed
            _original: {
                setTimeout: originalSetTimeout,
                setInterval: originalSetInterval,
                clearTimeout: originalClearTimeout,
                clearInterval: originalClearInterval,
                WebSocket: require('ws')
            }
        };
    }

    /**
     * Load a plugin with resource tracking
     */
    async loadPlugin(pluginPath) {
        const pluginName = path.basename(pluginPath, '.js');
        logInfo(`Plugin Manager: Loading plugin ${pluginName} from ${pluginPath}`);
        
        try {
            // Set current plugin for context tracking
            this.currentPlugin = pluginName;
            
            // Clear module cache for this plugin to enable hot reloading
            this.clearPluginFromCache(pluginName);
            
            logInfo(`Loading plugin: ${pluginName}`);
            
            // Create plugin context with resource tracking
            const context = this.createPluginContext(pluginName);
            
            // Store original global functions
            const originalGlobals = {
                setTimeout: global.setTimeout,
                setInterval: global.setInterval,
                clearTimeout: global.clearTimeout,
                clearInterval: global.clearInterval,
                WebSocket: global.WebSocket,
                __plugin: global.__plugin
            };

            // Temporarily replace global functions with tracked versions
            global.setTimeout = context.setTimeout;
            global.setInterval = context.setInterval;
            global.clearTimeout = context.clearTimeout;
            global.clearInterval = context.clearInterval;
            global.WebSocket = context.WebSocket;
            global.__plugin = context;
            
            try {
                // Load the plugin module
                const pluginModule = require(pluginPath);
                
                // Store the plugin instance
                this.loadedPlugins.set(pluginName, {
                    module: pluginModule,
                    path: pluginPath,
                    loadTime: Date.now(),
                    context: context,
                    version: this.extractPluginVersion(pluginPath)
                });

                logInfo(`Plugin ${pluginName} loaded successfully`);
                logInfo(`Plugin Manager: Total loaded plugins: ${this.loadedPlugins.size}`);
                
            } finally {
                // Restore original global functions
                global.setTimeout = originalGlobals.setTimeout;
                global.setInterval = originalGlobals.setInterval;
                global.clearTimeout = originalGlobals.clearTimeout;
                global.clearInterval = originalGlobals.clearInterval;
                global.WebSocket = originalGlobals.WebSocket;
                global.__plugin = originalGlobals.__plugin;

                this.currentPlugin = null;
            }
            
        } catch (error) {
            logError(`Failed to load plugin ${pluginName}: ${error.message}`);
            this.currentPlugin = null;
            throw error;
        }
    }

    /**
     * Unload a plugin and clean up all its resources
     */
    async unloadPlugin(pluginName) {
        try {
            logInfo(`Unloading plugin: ${pluginName}`);

            // Clean up timers
            const timers = this.pluginTimers.get(pluginName);
            if (timers) {
                timers.forEach(timerId => {
                    clearTimeout(timerId);
                    clearInterval(timerId);
                });
                timers.clear();
            }

            // Clean up WebSocket connections
            const webSockets = this.pluginWebSockets.get(pluginName);
            if (webSockets) {
                webSockets.forEach(ws => {
                    if (ws.readyState === ws.OPEN || ws.readyState === ws.CONNECTING) {
                        ws.close(1000, 'Plugin unloading');
                    }
                });
                webSockets.clear();
            }

            // Run custom cleanup handlers
            const cleanupHandlers = this.pluginCleanupHandlers.get(pluginName);
            if (cleanupHandlers) {
                for (const cleanupFn of cleanupHandlers) {
                    try {
                        await cleanupFn();
                    } catch (error) {
                        logError(`Error in cleanup handler for ${pluginName}: ${error.message}`);
                    }
                }
                cleanupHandlers.length = 0;
            }

            // Clear from module cache
            this.clearPluginFromCache(pluginName);

            // Remove from loaded plugins
            this.loadedPlugins.delete(pluginName);

            logInfo(`Plugin ${pluginName} unloaded successfully`);

        } catch (error) {
            logError(`Failed to unload plugin ${pluginName}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Restart a plugin (unload then load)
     */
    async restartPlugin(pluginName) {
        const plugin = this.loadedPlugins.get(pluginName);
        if (!plugin) {
            throw new Error(`Plugin ${pluginName} is not loaded`);
        }

        const pluginPath = plugin.path;
        await this.unloadPlugin(pluginName);
        await this.loadPlugin(pluginPath);
    }

    /**
     * Clear plugin modules from require cache
     */
    clearPluginFromCache(pluginName) {
        const cacheKeys = this.pluginModuleCache.get(pluginName) || [];
        cacheKeys.forEach(key => {
            delete require.cache[key];
        });
        this.pluginModuleCache.delete(pluginName);

        // Also clear any modules that might be related to this plugin
        Object.keys(require.cache).forEach(key => {
            if (key.includes(pluginName) || key.includes('plugins')) {
                delete require.cache[key];
            }
        });
    }

    /**
     * Get plugin status information
     */
    getPluginStatus(pluginName) {
        const plugin = this.loadedPlugins.get(pluginName);
        if (!plugin) {
            return { status: 'unloaded' };
        }

        const timers = this.pluginTimers.get(pluginName);
        const webSockets = this.pluginWebSockets.get(pluginName);
        const cleanupHandlers = this.pluginCleanupHandlers.get(pluginName);

        // Use plugin manager's tracking as primary source
        let pluginWebSocketCount = webSockets ? webSockets.size : 0;
        let pluginTimerCount = timers ? timers.size : 0;

        // Check for common plugin tracking patterns in global state
        // Look for global state objects that match this plugin's name pattern
        const pluginBaseName = path.basename(plugin.path, '.js').toLowerCase();

        // Check for plugin-specific global state (e.g., scannerState, spectrumGraphState)
        Object.keys(global).forEach(key => {
            const globalState = global[key];
            if (globalState && typeof globalState === 'object' &&
                key.toLowerCase().includes('state') &&
                (key.toLowerCase().includes(pluginBaseName.replace('_server', '')) ||
                 pluginBaseName.includes(key.toLowerCase().replace('state', '')))) {

                // Found a matching state object, check for socket tracking
                if (globalState.sockets && typeof globalState.sockets.size === 'number') {
                    pluginWebSocketCount = globalState.sockets.size;
                }

                // Check for timer tracking
                if (globalState.timers && typeof globalState.timers.size === 'number') {
                    pluginTimerCount = globalState.timers.size;
                }
                if (globalState.intervals && typeof globalState.intervals.size === 'number') {
                    pluginTimerCount += globalState.intervals.size;
                }
            }
        });

        return {
            status: 'loaded',
            loadTime: plugin.loadTime,
            timersCount: pluginTimerCount,
            webSocketsCount: pluginWebSocketCount,
            cleanupHandlersCount: cleanupHandlers ? cleanupHandlers.length : 0,
            version: plugin.version || this.extractPluginVersion(plugin.path)
        };
    }

    /**
     * Get all loaded plugins status
     */
    getAllPluginsStatus() {
        const status = {};
        this.loadedPlugins.forEach((plugin, pluginName) => {
            status[pluginName] = this.getPluginStatus(pluginName);
        });
        return status;
    }

    /**
     * Check if a plugin is hot-reloadable (has server-side code)
     */
    isPluginHotReloadable(pluginName) {
        return this.loadedPlugins.has(pluginName);
    }

    /**
     * Extract plugin display name using completely generic approach
     */
    extractPluginDisplayName(serverFilePath) {
        try {
            const pluginName = path.basename(serverFilePath, '.js');

            // Use the same approach as server/plugins.js - get all configs
            const { allPluginConfigs } = require('./plugins');
            const allConfigs = allPluginConfigs();

            // Generic approach: if server file is in a subdirectory,
            // look for any config file in the same directory
            const serverDir = path.dirname(serverFilePath);
            const pluginsDir = path.join(__dirname, '../plugins');

            if (serverDir !== pluginsDir) {
                // Server file is in a subdirectory - look for config in same directory
                const dirName = path.basename(serverDir);

                // Find any config that has frontEndPath pointing to this directory
                const configInSameDir = allConfigs.find(config => {
                    if (!config.frontEndPath) return false;
                    const configDir = path.dirname(config.frontEndPath);
                    return configDir === dirName || configDir === '';
                });

                if (configInSameDir && configInSameDir.name) {
                    return configInSameDir.name;
                }
            }

            // Fallback: use first available config name (for root-level plugins)
            const firstConfig = allConfigs.find(config => config.name);
            if (firstConfig && firstConfig.name) {
                return firstConfig.name;
            }

            return path.basename(serverFilePath, '.js');
        } catch (error) {
            return path.basename(serverFilePath, '.js');
        }
    }

    /**
     * Extract plugin version using completely generic approach
     */
    extractPluginVersion(serverFilePath) {
        try {
            // Use the same approach as server/plugins.js
            const { allPluginConfigs } = require('./plugins');
            const allConfigs = allPluginConfigs();

            // Generic approach: if server file is in a subdirectory,
            // look for any config file in the same directory
            const serverDir = path.dirname(serverFilePath);
            const pluginsDir = path.join(__dirname, '../plugins');

            if (serverDir !== pluginsDir) {
                // Server file is in a subdirectory - look for config in same directory
                const dirName = path.basename(serverDir);

                // Find any config that has frontEndPath pointing to this directory
                const configInSameDir = allConfigs.find(config => {
                    if (!config.frontEndPath) return false;
                    const configDir = path.dirname(config.frontEndPath);
                    return configDir === dirName || configDir === '';
                });

                if (configInSameDir && configInSameDir.version) {
                    return configInSameDir.version;
                }
            }

            // Fallback: use first available version
            const firstConfig = allConfigs.find(config => config.version);
            if (firstConfig && firstConfig.version) {
                return firstConfig.version;
            }

            return 'Unknown';
        } catch (error) {
            return 'Unknown';
        }
    }

    /**
     * Get the directory name for a plugin based on its server file name
     */
    getPluginDirectoryName(pluginName) {
        try {
            const pluginsDir = path.join(__dirname, '../plugins');
            const files = fs.readdirSync(pluginsDir);

            for (const file of files) {
                const filePath = path.join(pluginsDir, file);
                const stat = fs.statSync(filePath);

                if (stat.isDirectory()) {
                    const subFiles = fs.readdirSync(filePath);
                    if (subFiles.includes(`${pluginName}.js`)) {
                        return file;
                    }
                }
            }

            return null;
        } catch (error) {
            return null;
        }
    }

    /**
     * Extract version from a config file
     */
    extractVersionFromConfigFile(filePath) {
        try {
            const content = fs.readFileSync(filePath, 'utf8');

            // Look for var pluginConfig = { ... version: '...' ... }
            const versionMatch = content.match(/var\s+pluginConfig\s*=\s*{[^}]*version:\s*['"]([^'"]+)['"]/s);
            if (versionMatch) {
                return versionMatch[1];
            }

            return 'Unknown';
        } catch (error) {
            return 'Unknown';
        }
    }

    /**
     * Get list of all server-side plugins from filesystem
     */
    getServerSidePlugins() {
        const pluginsDir = path.join(__dirname, '../plugins');
        const plugins = [];

        try {
            const files = fs.readdirSync(pluginsDir);
            files.forEach(file => {
                const filePath = path.join(pluginsDir, file);
                const stat = fs.statSync(filePath);

                if (stat.isFile() && file.endsWith('_server.js')) {
                    // Direct server file in plugins directory
                    const pluginName = path.basename(file, '.js');
                    const displayName = this.extractPluginDisplayName(filePath);
                    const isLoaded = this.loadedPlugins.has(pluginName);

                    const version = isLoaded ?
                        this.loadedPlugins.get(pluginName)?.version || this.extractPluginVersion(pluginPath) :
                        this.extractPluginVersion(pluginPath);

                    plugins.push({
                        name: pluginName,
                        displayName: displayName,
                        path: filePath,
                        isLoaded: isLoaded,
                        version: version,
                        status: isLoaded ? this.getPluginStatus(pluginName) : { status: 'unloaded', version: version }
                    });
                } else if (stat.isDirectory()) {
                    // Check subdirectory for server files
                    try {
                        const subFiles = fs.readdirSync(filePath);
                        subFiles.forEach(subFile => {
                            if (subFile.endsWith('_server.js')) {
                                const pluginName = path.basename(subFile, '.js');
                                const subFilePath = path.join(filePath, subFile);
                                const displayName = this.extractPluginDisplayName(subFilePath);
                                const isLoaded = this.loadedPlugins.has(pluginName);

                                const version = isLoaded ?
                                    this.loadedPlugins.get(pluginName)?.version || this.extractPluginVersion(pluginPath) :
                                    this.extractPluginVersion(pluginPath);

                                plugins.push({
                                    name: pluginName,
                                    displayName: displayName,
                                    path: subFilePath,
                                    isLoaded: isLoaded,
                                    version: version,
                                    status: isLoaded ? this.getPluginStatus(pluginName) : { status: 'unloaded', version: version }
                                });
                            }
                        });
                    } catch (subError) {
                        // Ignore subdirectory read errors
                    }
                }
            });

            // Also check for any loaded plugins that might not be found in filesystem scan
            this.loadedPlugins.forEach((plugin, pluginName) => {
                const existingPlugin = plugins.find(p => p.name === pluginName);
                if (!existingPlugin) {
                    const displayName = this.extractPluginDisplayName(plugin.path);
                    const version = plugin.version || this.extractPluginVersion(plugin.path);
                    plugins.push({
                        name: pluginName,
                        displayName: displayName,
                        path: plugin.path,
                        isLoaded: true,
                        version: version,
                        status: this.getPluginStatus(pluginName)
                    });
                }
            });

            // Check for ALL plugins in config (enabled and disabled) so they can be loaded on demand
            try {
                const { allPluginConfigs } = require('./plugins');
                const allConfigs = allPluginConfigs();

                allConfigs.forEach(config => {
                    if (config.name && config.frontEndPath) {
                        // Look for corresponding server file
                        const configDir = path.dirname(config.frontEndPath);
                        const frontEndFile = path.basename(config.frontEndPath, '.js');

                        const possibleServerFiles = [
                            // Standard patterns
                            path.join(pluginsDir, configDir, `${configDir}_server.js`),
                            path.join(pluginsDir, configDir, `${configDir.toLowerCase()}_server.js`),
                            path.join(pluginsDir, configDir, 'server.js'),
                            path.join(pluginsDir, configDir, 'frontend_server.js'),
                            path.join(pluginsDir, `${configDir}_server.js`),
                            path.join(pluginsDir, `${configDir.toLowerCase()}_server.js`),
                            // Frontend file name based patterns
                            path.join(pluginsDir, configDir, `${frontEndFile}_server.js`),
                            // Special cases for known plugins
                            path.join(pluginsDir, configDir, `plugin${configDir}_server.js`),
                            path.join(pluginsDir, configDir, `plugin${configDir.charAt(0).toUpperCase() + configDir.slice(1)}_server.js`)
                        ];

                        for (const serverFile of possibleServerFiles) {
                            if (fs.existsSync(serverFile)) {
                                const serverFileName = path.basename(serverFile, '.js');
                                const isLoaded = this.loadedPlugins.has(serverFileName);


                                // Better deduplication: check by name, path, AND display name (case-insensitive)
                                const isDuplicate = plugins.some(p =>
                                    p.name.toLowerCase() === serverFileName.toLowerCase() ||
                                    p.path.toLowerCase() === serverFile.toLowerCase() ||
                                    (p.displayName === config.name && p.name.toLowerCase().includes(configDir.toLowerCase()))
                                );

                                if (!isDuplicate) {
                                    // Found a server file for a plugin that's not already in the list
                                    // Include ALL plugins (enabled/disabled, loaded/unloaded) so they can be managed
                                    plugins.push({
                                        name: serverFileName,
                                        displayName: config.name,
                                        path: serverFile,
                                        isLoaded: isLoaded,
                                        version: isLoaded ?
                                            (this.loadedPlugins.get(serverFileName)?.version || config.version || 'Unknown') :
                                            (config.version || 'Unknown'),
                                        status: isLoaded ? this.getPluginStatus(serverFileName) : { status: 'unloaded' }
                                    });
                                }
                                break;
                            }
                        }
                    }
                });
            } catch (error) {
                logWarn(`Could not check for plugins: ${error.message}`);
            }

        } catch (error) {
            logError(`Error reading plugins directory: ${error.message}`);
        }



        return plugins;
    }
}

// Create singleton instance
const pluginManager = new PluginManager();

module.exports = pluginManager;
