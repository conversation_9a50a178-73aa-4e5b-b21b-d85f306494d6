/**
 * WEATHER PLUGIN FOR FM-DX WEBSERVER
 * Author: NoobishSVK
 * Used API: https://open-meteo.com/
 * Icons: OpenWeatherMap
 */

'use strict';

const WebSocket = require('ws');
const https = require('https');
const config = require('./../../config.json');
const { logInfo, logWarn, logError } = require('../../server/console');
const webserverPort = config.webserver.webserverPort || 8080;
const externalWsUrl = `ws://127.0.0.1:${webserverPort}`;
const dataPluginsSocket = new WebSocket(externalWsUrl + '/data_plugins');
const LAT = config.identification.lat;
const LON = config.identification.lon;
const weatherUrl = `https://api.open-meteo.com/v1/forecast?latitude=${LAT}&longitude=${LON}&current=temperature_2m,relative_humidity_2m,is_day,weather_code,pressure_msl,wind_speed_10m,wind_direction_10m`;
let cachedWeather = null;
let lastSendTime = 0;

if (!LAT || !LON) logError('[Weather plugin] Latitude/Longitude not set');

// Fetch and cache weather data
function fetchWeatherMessage(callback) {
  https.get(weatherUrl, (res) => {
    let rawData = '';
    res.on('data', (chunk) => rawData += chunk);
    res.on('end', () => {
      try {
        const parsed = JSON.parse(rawData);
        const message = {
          type: 'Weather',
          ...parsed
        };
        cachedWeather = message;
        if (callback) callback(message);
      } catch (e) {
        if (LAT && LON) logError('[Weather plugin] Error parsing weather response - ' + e.message);
      }
    });
  }).on('error', (e) => {
    if (LAT && LON) logError('[Weather plugin] ' + (e.message || 'Error'));
  });
}

// Schedule fetch
function scheduleNextFetch() {
  const now = new Date();
  const minutes = now.getMinutes();
  const initialDelay = (15 - (minutes % 15)) * 60 * 1000;
  setTimeout(() => {
    fetchWeatherMessage();
    setInterval(fetchWeatherMessage, 15 * 60 * 1000);
  }, initialDelay);
}

// When connected to /data_plugins
dataPluginsSocket.on('open', () => {
  logInfo('Weather plugin connected to /data_plugins');
  fetchWeatherMessage();
  scheduleNextFetch();
});

// Respond to browser requests
dataPluginsSocket.on('message', (message) => {
  try {
    const parsed = JSON.parse(message);
    if (parsed.type === 'Plugin-Weather-Request') {
      const now = Date.now();
      if (cachedWeather && now - lastSendTime >= 100) {
        dataPluginsSocket.send(JSON.stringify(cachedWeather));
        lastSendTime = now;
      }
    }
  } catch (err) {
    if (LAT && LON) logError('[Weather plugin] Invalid message: ' + err.message);
  }
});
