/**
 * WEATHER PLUGIN FOR FM-DX WEBSERVER
 * Author: NoobishSVK
 * Used API: https://open-meteo.com/
 * Icons: OpenWeatherMap
 */

'use strict';


// Hot reload state management
if (!global.FrontendServerState_92726be2) {
    global.FrontendServerState_92726be2 = {
        timers: new Set(),
        sockets: new Set(),
        originalHandleData: null,
        routeAdded: false,
        isInitialized: false,
        textSocket: null,
        extraSocket: null
    };
}

// const variables
const debug = false;

// Tracked timer functions for hot reload
const originalSetTimeout = setTimeout;
const originalSetInterval = setInterval;

function trackedSetTimeout(callback, delay, ...args) {
    const timerId = originalSetTimeout((...callbackArgs) => {
        global.FrontendServerState_92726be2.timers.delete(timerId);
        callback(...callbackArgs);
    }, delay, ...args);
    global.FrontendServerState_92726be2.timers.add(timerId);
    return timerId;
}

function trackedSetInterval(callback, delay, ...args) {
    const timerId = originalSetInterval(callback, delay, ...args);
    global.FrontendServerState_92726be2.timers.add(timerId);
    return timerId;
}

const WebSocket = require('ws');
const originalWebSocket = WebSocket;

function trackedWebSocket(url, protocols) {
    const socket = new originalWebSocket(url, protocols);
    global.FrontendServerState_92726be2.sockets.add(socket);
    logInfo(`Plugin: Created WebSocket to ${url}, total: ${global.FrontendServerState_92726be2.sockets.size}`);

    socket.addEventListener('close', () => {
        global.FrontendServerState_92726be2.sockets.delete(socket);
        logInfo(`Plugin: WebSocket closed, remaining: ${global.FrontendServerState_92726be2.sockets.size}`);
    });

    return socket;
}

const https = require('https');
const config = require('./../../config.json');
const { logInfo, logWarn, logError } = require('../../server/console');
const webserverPort = config.webserver.webserverPort || 8080;
const externalWsUrl = `ws://127.0.0.1:${webserverPort}`;
global.FrontendServerState_92726be2.dataPluginsSocket = trackedWebSocket(externalWsUrl + '/data_plugins');
const dataPluginsSocket = global.FrontendServerState_92726be2.dataPluginsSocket;
const LAT = config.identification.lat;
const LON = config.identification.lon;
const weatherUrl = `https://api.open-meteo.com/v1/forecast?latitude=${LAT}&longitude=${LON}&current=temperature_2m,relative_humidity_2m,is_day,weather_code,pressure_msl,wind_speed_10m,wind_direction_10m`;
let cachedWeather = null;
let lastSendTime = 0;

if (!LAT || !LON) logError('[Weather plugin] Latitude/Longitude not set');

// Fetch and cache weather data
function fetchWeatherMessage(callback) {
  https.get(weatherUrl, (res) => {
    let rawData = '';
    res.on('data', (chunk) => rawData += chunk);
    res.on('end', () => {
      try {
        const parsed = JSON.parse(rawData);
        const message = {
          type: 'Weather',
          ...parsed
        };
        cachedWeather = message;
        if (callback) callback(message);
      } catch (e) {
        if (LAT && LON) logError('[Weather plugin] Error parsing weather response - ' + e.message);
      }
    });
  }).on('error', (e) => {
    if (LAT && LON) logError('[Weather plugin] ' + (e.message || 'Error'));
  });
}

// Schedule fetch
function scheduleNextFetch() {
  const now = new Date();
  const minutes = now.getMinutes();
  const initialDelay = (15 - (minutes % 15)) * 60 * 1000;
  trackedSetTimeout(() => {
    fetchWeatherMessage();
    trackedSetInterval(fetchWeatherMessage, 15 * 60 * 1000);
  }, initialDelay);
}

// When connected to /data_plugins
dataPluginsSocket.on('open', () => {
  logInfo('Weather plugin connected to /data_plugins');
  fetchWeatherMessage();
  scheduleNextFetch();
});

// Respond to browser requests
dataPluginsSocket.on('message', (message) => {
  try {
    const parsed = JSON.parse(message);
    if (parsed.type === 'Plugin-Weather-Request') {
      const now = Date.now();
      if (cachedWeather && now - lastSendTime >= 100) {
        global.FrontendServerState_92726be2.dataPluginsSocket.send(JSON.stringify(cachedWeather));
        lastSendTime = now;
      }
    }
  } catch (err) {
    if (LAT && LON) logError('[Weather plugin] Invalid message: ' + err.message);
  }
});

// Cleanup function for hot reload
function cleanup() {
    logInfo('Plugin: Cleaning up for hot reload...');

    // Clear all timers
    global.FrontendServerState_92726be2.timers.forEach(timer => {
        clearTimeout(timer);
    });
    global.FrontendServerState_92726be2.timers.clear();

    // Close all WebSocket connections
    logInfo(`Plugin: Closing ${global.FrontendServerState_92726be2.sockets.size} WebSocket connections...`);
    global.FrontendServerState_92726be2.sockets.forEach(socket => {
        if (socket && socket.readyState === 1) {
            socket.close(1000, 'Plugin reloading');
        }
    });
    global.FrontendServerState_92726be2.sockets.clear();

    // Clear WebSocket variables
    global.FrontendServerState_92726be2.dataPluginsSocket = null;
    logInfo('Plugin: WebSocket cleanup completed');

    // Restore original dataHandler if it exists
    if (global.FrontendServerState_92726be2.originalHandleData) {
        datahandlerReceived.handleData = global.FrontendServerState_92726be2.originalHandleData;
        logInfo('Plugin: Restored original dataHandler');
    }

    global.FrontendServerState_92726be2.isInitialized = false;
    logInfo('Plugin: Cleanup completed');
}

// Register cleanup with hot reload system
if (typeof __plugin !== 'undefined' && __plugin.registerCleanup) {
    __plugin.registerCleanup(cleanup);
}

// Prevent multiple initializations
if (global.FrontendServerState_92726be2.isInitialized) {
    logInfo('Plugin: Already initialized, cleaning up previous instance...');
    cleanup();
}

global.FrontendServerState_92726be2.isInitialized = true;
