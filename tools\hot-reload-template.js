#!/usr/bin/env node

/*
    Hot Reload Plugin Template Generator
    Creates a hot reload compatible plugin template
    
    Usage: node tools/create-hot-reload-template.js <plugin-name>
    Example: node tools/create-hot-reload-template.js "My New Plugin"
*/

const fs = require('fs');
const path = require('path');

function createHotReloadTemplate(pluginName) {
    const safeName = pluginName.replace(/[^a-zA-Z0-9]/g, '');
    const stateVarName = safeName + 'State';
    const folderName = pluginName.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '');
    const fileName = `${safeName}_server.js`;
    
    const template = `/*
    ${pluginName} - Hot Reload Compatible
    Generated by Hot Reload Template Generator
    
    //// Server-side code ////
*/

'use strict';

const pluginName = "${pluginName}";

// Hot reload state management
if (!global.${stateVarName}) {
    global.${stateVarName} = {
        timers: new Set(),
        sockets: new Set(),
        originalHandleData: null,
        routeAdded: false,
        isInitialized: false
    };
}

// Library imports
const express = require('express');
const fs = require('fs');
const path = require('path');
const WebSocket = require('ws');

// File imports
const config = require('./../../config.json');
const { logInfo, logWarn, logError } = require('../../server/console');
const endpointsRouter = require('../../server/endpoints');

// Plugin variables
let pluginConfig = {};

// Helper functions for hot reload compatibility
function createTimer(func, delay, isInterval = false) {
    const timer = isInterval ? setInterval(func, delay) : setTimeout(func, delay);
    global.${stateVarName}.timers.add(timer);
    return timer;
}

function createWebSocket(url) {
    const socket = new WebSocket(url);
    global.${stateVarName}.sockets.add(socket);
    
    // Auto-cleanup on close
    socket.on('close', () => {
        global.${stateVarName}.sockets.delete(socket);
    });
    
    return socket;
}

function clearTimer(timer) {
    clearTimeout(timer);
    clearInterval(timer);
    global.${stateVarName}.timers.delete(timer);
}

// Cleanup function for hot reload
function cleanup() {
    logInfo(\`\${pluginName}: Cleaning up for hot reload...\`);

    // Clear all timers
    global.${stateVarName}.timers.forEach(timer => {
        clearTimeout(timer);
        clearInterval(timer);
    });
    global.${stateVarName}.timers.clear();

    // Close all WebSocket connections
    if (global.${stateVarName}.sockets.size > 0) {
        logInfo(\`\${pluginName}: Closing \${global.${stateVarName}.sockets.size} WebSocket connections...\`);
        global.${stateVarName}.sockets.forEach(socket => {
            if (socket && socket.readyState === 1) { // WebSocket.OPEN = 1
                socket.close(1000, 'Plugin reloading');
            }
        });
        global.${stateVarName}.sockets.clear();
    }

    // Restore original handlers if modified
    if (global.${stateVarName}.originalHandleData) {
        // Restore your original handlers here
        logInfo(\`\${pluginName}: Restored original handlers\`);
    }

    global.${stateVarName}.isInitialized = false;
    logInfo(\`\${pluginName}: Cleanup completed\`);
}

// Register cleanup with hot reload system
if (typeof __plugin !== 'undefined' && __plugin.registerCleanup) {
    __plugin.registerCleanup(cleanup);
}

// Prevent multiple initializations
if (global.${stateVarName}.isInitialized) {
    logInfo(\`\${pluginName}: Already initialized, cleaning up previous instance...\`);
    cleanup();
}
global.${stateVarName}.isInitialized = true;

// Your plugin code goes here
function initializePlugin() {
    logInfo(\`\${pluginName}: Initializing...\`);
    
    // Example: Create a timer (hot reload compatible)
    createTimer(() => {
        logInfo(\`\${pluginName}: Timer tick\`);
    }, 5000, true); // true = setInterval, false = setTimeout
    
    // Example: Create a WebSocket (hot reload compatible)
    const socket = createWebSocket('ws://127.0.0.1:8080/data_plugins');
    socket.on('open', () => {
        logInfo(\`\${pluginName}: WebSocket connected\`);
    });
    
    // Example: Add custom route (only once)
    if (!global.${stateVarName}.routeAdded) {
        endpointsRouter.get('/${safeName.toLowerCase()}', (req, res) => {
            res.json({ status: 'ok', plugin: pluginName });
        });
        global.${stateVarName}.routeAdded = true;
        logInfo(\`\${pluginName}: Custom route added\`);
    }
    
    logInfo(\`\${pluginName}: Plugin loaded successfully with hot reload support!\`);
}

// Initialize the plugin
initializePlugin();

// Export functions if needed
module.exports = {
    // Export your functions here
};
`;

    const pluginDir = path.join('plugins', folderName);
    const filePath = path.join(pluginDir, fileName);
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(pluginDir)) {
        fs.mkdirSync(pluginDir, { recursive: true });
    }
    
    // Write template file
    fs.writeFileSync(filePath, template);
    
    console.log(`✅ Hot reload compatible plugin template created!`);
    console.log(`📁 Location: ${filePath}`);
    console.log(`🔥 Plugin: ${pluginName}`);
    console.log(`\n📝 Next steps:`);
    console.log(`   1. Edit ${fileName} and add your plugin logic`);
    console.log(`   2. Use createTimer() instead of setTimeout/setInterval`);
    console.log(`   3. Use createWebSocket() instead of new WebSocket()`);
    console.log(`   4. Test hot reload with the Plugin Management interface`);
}

// CLI usage
if (require.main === module) {
    const pluginName = process.argv[2];
    if (!pluginName) {
        console.log(`Usage: node ${path.basename(__filename)} "<plugin-name>"`);
        console.log(`Example: node ${path.basename(__filename)} "My New Plugin"`);
        process.exit(1);
    }
    
    createHotReloadTemplate(pluginName);
}

module.exports = { createHotReloadTemplate };
