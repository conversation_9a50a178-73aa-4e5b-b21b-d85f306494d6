#!/usr/bin/env node

/*
    Hot Reload Compatibility Converter v2
    Converts FM-DX-Webserver plugins to match the exact working version structure
    
    Usage: node tools/make-hot-reload-compatible-v2.js <plugin-server-file>
*/

const fs = require('fs');
const path = require('path');

function convertToHotReloadCompatible(filePath) {
    if (!fs.existsSync(filePath)) {
        console.error(`❌ File not found: ${filePath}`);
        process.exit(1);
    }

    console.log(`🔄 Converting ${filePath} to hot reload compatible...`);

    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content; // Store original before any modifications

    // Detect existing line ending style
    const hasWindowsLineEndings = content.includes('\r\n');
    const hasUnixLineEndings = content.includes('\n') && !content.includes('\r\n');
    const lineEnding = hasWindowsLineEndings ? '\r\n' : '\n';

    console.log(`📝 Detected line endings: ${hasWindowsLineEndings ? 'Windows (CRLF)' : 'Unix (LF)'}`);

    // Normalize to Unix line endings for processing
    content = content.replace(/\r\n/g, '\n');
    
    // Extract plugin name
    let pluginNameMatch = content.match(/const\s+pluginName\s*=\s*["']([^"']+)["']/);
    let pluginName = pluginNameMatch ? pluginNameMatch[1] : 'Unknown Plugin';
    
    // If no pluginName found, try to detect from filename
    if (!pluginNameMatch) {
        const fileName = path.basename(filePath, '.js');
        if (fileName.toLowerCase().includes('spectrum')) {
            pluginName = 'Spectrum Graph';
        } else if (fileName.toLowerCase().includes('scanner')) {
            pluginName = 'Scanner';
        } else {
            pluginName = fileName.replace(/[_-]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }
    }
    
    const stateVarName = pluginName.replace(/[^a-zA-Z0-9]/g, '') + 'State';
    
    console.log(`📝 Plugin: ${pluginName}`);
    console.log(`🔧 State variable: ${stateVarName}`);
    
    // Step 1: Don't add pluginName if missing - stay generic
    
    // Step 2: Add state management right after pluginName (or at the top if no pluginName)
    const stateManagement = `
// Hot reload state management
if (!global.${stateVarName}) {
    global.${stateVarName} = {
        timers: new Set(),
        sockets: new Set(),
        originalHandleData: null,
        routeAdded: false,
        isInitialized: false,
        textSocket: null,
        extraSocket: null
    };
}
`;

    const hasPluginName = content.match(/(const pluginName = ["'][^"']+["'];)/);
    if (hasPluginName) {
        // Add after pluginName if it exists
        content = content.replace(
            /(const pluginName = ["'][^"']+["'];)/,
            `$1${stateManagement}`
        );
    } else {
        // Add at the beginning if no pluginName (after first require statement)
        const firstRequirePattern = /(const [^=]+ = require\(['"][^'"]+['"]\);)/;
        content = content.replace(firstRequirePattern, `$1${stateManagement}`);
    }
    
    // Step 3: Find the imports section and add hot reload code after it
    const importsEndPattern = /const endpointsRouter = require\(['"][^'"]+['"]\);/;
    const importsEndMatch = content.match(importsEndPattern);
    
    if (importsEndMatch) {
        const hotReloadCode = `
// Plugin variables - declared early to avoid initialization errors in cleanup
let extraSocket, textSocket, textSocketLost, messageParsed, messageParsedTimeout, startTime, tuningLowerLimitScan, tuningUpperLimitScan, tuningLowerLimitOffset, tuningUpperLimitOffset, debounceTimer, ipTimeout;

// Cleanup function for hot reload (defined after imports and variables)
function cleanup() {
    logInfo('Plugin: Cleaning up for hot reload...');

    // Clear all timers
    global.${stateVarName}.timers.forEach(timer => {
        clearTimeout(timer);
        clearInterval(timer);
    });
    global.${stateVarName}.timers.clear();

    // Close all WebSocket connections
    logInfo(\`Plugin: Closing \${global.${stateVarName}.sockets.size} WebSocket connections...\`);
    global.${stateVarName}.sockets.forEach(socket => {
        if (socket && socket.readyState === 1) { // WebSocket.OPEN = 1
            socket.close(1000, 'Plugin reloading');
        }
    });
    global.${stateVarName}.sockets.clear();

    // Clear WebSocket variables to prevent reuse (stored in global state)
    global.${stateVarName}.textSocket = null;
    global.${stateVarName}.extraSocket = null;
    logInfo('Plugin: WebSocket cleanup completed');

    // Restore original dataHandler if it exists
    if (global.${stateVarName}.originalHandleData) {
        datahandlerReceived.handleData = global.${stateVarName}.originalHandleData;
        logInfo('Plugin: Restored original dataHandler');
    }

    global.${stateVarName}.isInitialized = false;
    logInfo('Plugin: Cleanup completed');
}

// Register cleanup with hot reload system
if (typeof __plugin !== 'undefined' && __plugin.registerCleanup) {
    __plugin.registerCleanup(cleanup);
}

// Prevent multiple initializations
if (global.${stateVarName}.isInitialized) {
    logInfo('Plugin: Already initialized, cleaning up previous instance...');
    cleanup();
}

global.${stateVarName}.isInitialized = true;
`;
        
        content = content.replace(importsEndPattern, `$&${hotReloadCode}`);
    }
    
    // Step 4: Remove duplicate variable declarations
    const variablePattern = /\/\/ let variables[\s\S]*?let extraSocket[^;]*;/;
    content = content.replace(variablePattern, '// let variables');
    
    // Step 5: Add helper functions after initialization (check each const variable individually)
    const hasDebug = content.includes('const debug');
    const hasWebserverPort = content.includes('const webserverPort');
    const hasExternalWsUrl = content.includes('const externalWsUrl');

    console.log(`🔍 Existing const variables - debug: ${hasDebug}, webserverPort: ${hasWebserverPort}, externalWsUrl: ${hasExternalWsUrl}`);

    let constVariablesSection = '';
    if (!hasDebug || !hasWebserverPort || !hasExternalWsUrl) {
        constVariablesSection += '\n// const variables\n';
        if (!hasDebug) constVariablesSection += 'const debug = false;\n';
        if (!hasWebserverPort) constVariablesSection += 'const webserverPort = config.webserver.webserverPort || 8080;\n';
        if (!hasExternalWsUrl) constVariablesSection += 'const externalWsUrl = `ws://127.0.0.1:${webserverPort}`;\n';
        constVariablesSection += '\n';
    }

    const helperFunctionsSection = `${constVariablesSection}// Tracked timer functions for hot reload
const originalSetTimeout = setTimeout;
const originalSetInterval = setInterval;
function trackedSetTimeout(callback, delay, ...args) {
    const timerId = originalSetTimeout((...callbackArgs) => {
        global.${stateVarName}.timers.delete(timerId);
        callback(...callbackArgs);
    }, delay, ...args);
    global.${stateVarName}.timers.add(timerId);
    return timerId;
}

function trackedSetInterval(callback, delay, ...args) {
    const timerId = originalSetInterval(callback, delay, ...args);
    global.${stateVarName}.timers.add(timerId);
    return timerId;
}
`;

    // Find where to insert helper functions - after isInitialized = true
    const initPattern = new RegExp(`(global\\.${stateVarName}\\.isInitialized = true;)`);
    if (initPattern.test(content)) {
        content = content.replace(initPattern, `$1${helperFunctionsSection}`);
    } else {
        // Fallback: insert after state management block
        const stateBlockEnd = content.indexOf('};') + 2;
        if (stateBlockEnd > 1) {
            content = content.slice(0, stateBlockEnd) + helperFunctionsSection + content.slice(stateBlockEnd);
        }
    }
    
    // Step 6: Detect WebSocket variables and collect them
    const webSocketVars = new Set();
    const webSocketPattern = /(?:(const|let|var)\s+)?(\w+)\s*=\s*new\s+WebSocket\s*\(/g;
    let match;
    while ((match = webSocketPattern.exec(content)) !== null) {
        webSocketVars.add(match[2]); // Add variable name to set
    }

    console.log(`🔍 Detected WebSocket variables: ${Array.from(webSocketVars).join(', ')}`);

    // Step 7: Convert timer calls first
    content = content.replace(/setTimeout\s*\(/g, 'trackedSetTimeout(');
    content = content.replace(/setInterval\s*\(/g, 'trackedSetInterval(');

    // Step 8: Convert WebSocket assignments using a single comprehensive replacement
    // Process each WebSocket variable and replace ALL its patterns in one pass
    webSocketVars.forEach(varName => {
        // Replace all WebSocket assignment patterns for this variable in one go
        const allPatternsRegex = new RegExp(
            `(const\\s+${varName}\\s*=\\s*new\\s+WebSocket\\s*\\()|` +
            `((let|var)\\s+${varName}\\s*=\\s*new\\s+WebSocket\\s*\\()|` +
            `(\\b${varName}\\s*=\\s*new\\s+WebSocket\\s*\\()`,
            'g'
        );

        content = content.replace(allPatternsRegex, (fullMatch) => {
            return `global.${stateVarName}.${varName} = new WebSocket(`;
        });

        // Convert variable checks in conditions: if (!varName || varName.readyState...)
        const checkPattern = new RegExp(`\\b${varName}(?=\\s*\\|\\||\\s*&&|\\s*\\?)`, 'g');
        content = content.replace(checkPattern, (match, offset) => {
            // Don't replace if already part of global.state.varName
            const before = content.substring(Math.max(0, offset - 20), offset);
            if (before.includes(`global.${stateVarName}.`)) {
                return match;
            }
            return `global.${stateVarName}.${varName}`;
        });

        // Convert readyState checks: varName.readyState -> global.state.varName.readyState
        const readyStatePattern = new RegExp(`\\b${varName}\\.readyState`, 'g');
        content = content.replace(readyStatePattern, (match, offset) => {
            // Don't replace if already part of global.state.varName
            const before = content.substring(Math.max(0, offset - 20), offset);
            if (before.includes(`global.${stateVarName}.`)) {
                return match;
            }
            return `global.${stateVarName}.${varName}.readyState`;
        });
    });

    // Step 9: Convert WebSocket creation calls and add local assignments
    content = content.replace(/new\s+WebSocket\s*\(/g, 'trackedWebSocket(');

    // Add local assignments after global WebSocket assignments
    webSocketVars.forEach(varName => {
        const globalAssignPattern = new RegExp(`(global\\.${stateVarName}\\.${varName}\\s*=\\s*trackedWebSocket\\([^)]+\\);)`, 'g');
        content = content.replace(globalAssignPattern, `$1\n            ${varName} = global.${stateVarName}.${varName};`);
    });

    // Step 10: Add WebSocket backup and trackedWebSocket function after WebSocket import
    const webSocketImportPattern = /(const WebSocket = require\(['"]ws['"]\);)/;
    const webSocketImportMatch = content.match(webSocketImportPattern);

    console.log(`🔍 WebSocket import found: ${!!webSocketImportMatch}`);

    if (webSocketImportMatch) {
        const webSocketBackupAndFunction = `
const originalWebSocket = WebSocket;

function trackedWebSocket(url, protocols) {
    const socket = new originalWebSocket(url, protocols);
    global.${stateVarName}.sockets.add(socket);
    logInfo(\`Plugin: Created WebSocket to \${url}, total: \${global.${stateVarName}.sockets.size}\`);

    socket.addEventListener('close', () => {
        global.${stateVarName}.sockets.delete(socket);
        logInfo(\`Plugin: WebSocket closed, remaining: \${global.${stateVarName}.sockets.size}\`);
    });

    return socket;
}
`;
        content = content.replace(webSocketImportPattern, `$1${webSocketBackupAndFunction}`);
    }

    // Step 11: Add cleanup function with detected WebSocket variables
    const webSocketVarsList = Array.from(webSocketVars);
    const webSocketCleanupLines = webSocketVarsList.map(varName =>
        `    global.${stateVarName}.${varName} = null;`
    ).join('\n');

    const cleanupFunction = `
// Cleanup function for hot reload
function cleanup() {
    logInfo('Plugin: Cleaning up for hot reload...');

    // Clear all timers
    global.${stateVarName}.timers.forEach(timer => {
        clearTimeout(timer);
    });
    global.${stateVarName}.timers.clear();

    // Close all WebSocket connections
    logInfo(\`Plugin: Closing \${global.${stateVarName}.sockets.size} WebSocket connections...\`);
    global.${stateVarName}.sockets.forEach(socket => {
        if (socket && socket.readyState === 1) { // WebSocket.OPEN = 1
            socket.close(1000, 'Plugin reloading');
        }
    });
    global.${stateVarName}.sockets.clear();

    // Clear WebSocket variables to prevent reuse
${webSocketCleanupLines}
    logInfo('Plugin: WebSocket cleanup completed');

    // Restore original dataHandler if it exists
    if (global.${stateVarName}.originalHandleData) {
        datahandlerReceived.handleData = global.${stateVarName}.originalHandleData;
        logInfo('Plugin: Restored original dataHandler');
    }

    global.${stateVarName}.isInitialized = false;
    logInfo('Plugin: Cleanup completed');
}

// Register cleanup with hot reload system
if (typeof __plugin !== 'undefined' && __plugin.registerCleanup) {
    __plugin.registerCleanup(cleanup);
}

// Prevent multiple initializations
if (global.${stateVarName}.isInitialized) {
    logInfo('Plugin: Already initialized, cleaning up previous instance...');
    cleanup();
}

global.${stateVarName}.isInitialized = true;
`;

    // Add cleanup function after WebSocket backup and trackedWebSocket function
    if (webSocketImportMatch) {
        content = content.replace(
            /(function trackedWebSocket\([^}]+\}\s*)/,
            `$1${cleanupFunction}`
        );
    }

    // Step 12: Convert WebSocket constants to numeric values (Node.js compatibility)
    content = content.replace(/WebSocket\.OPEN/g, '1');
    content = content.replace(/WebSocket\.CLOSED/g, '3');
    content = content.replace(/WebSocket\.CONNECTING/g, '0');
    content = content.replace(/WebSocket\.CLOSING/g, '2');
    
    // Step 7: Handle dataHandler backup
    content = content.replace(
        /(const originalHandleData = datahandlerReceived\.handleData;)/,
        `$1
// Store original handler for cleanup
if (!global.${stateVarName}.originalHandleData) {
    global.${stateVarName}.originalHandleData = datahandlerReceived.handleData;
}`
    );
    
    // Restore original line endings
    if (lineEnding === '\r\n') {
        content = content.replace(/\n/g, '\r\n');
    }

    // Create backup and save
    const backupPath = filePath + '.backup';
    fs.writeFileSync(backupPath, originalContent);
    fs.writeFileSync(filePath, content);
    
    // Count conversions
    const timerMatches = (originalContent.match(/(setTimeout|setInterval)/g) || []).length;
    const socketMatches = (originalContent.match(/new\s+WebSocket/g) || []).length;
    
    console.log(`✅ Conversion completed!`);
    console.log(`📁 Original backed up to: ${backupPath}`);
    console.log(`🔥 Plugin is now hot reload compatible!`);
    console.log(`\n📊 Conversion Summary:`);
    console.log(`   • ${timerMatches} timer calls converted`);
    console.log(`   • ${socketMatches} WebSocket creations converted`);
    console.log(`   • Added global state management`);
    console.log(`   • Added cleanup function`);
    console.log(`   • Added initialization guards`);
    console.log(`\n⚠️  Note: Generic conversion - only assumes FM-DX-Webserver framework functions`);
    console.log(`   Manual verification recommended for plugin-specific patterns`);
}

// CLI usage
if (require.main === module) {
    const filePath = process.argv[2];
    if (!filePath) {
        console.log(`Usage: node ${path.basename(__filename)} <plugin-server-file>`);
        process.exit(1);
    }
    
    convertToHotReloadCompatible(filePath);
}

module.exports = { convertToHotReloadCompatible };
