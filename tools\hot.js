const fs = require('fs');
const path = require('path');

function convertToHotReloadCompatible(filePath) {
    if (!fs.existsSync(filePath)) {
        console.error(`❌ File not found: ${filePath}`);
        process.exit(1);
    }

    console.log(`🔄 Converting ${filePath} to hot reload compatible...`);

    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;

    // Detect existing line ending style
    const hasWindowsLineEndings = content.includes('\r\n');
    const lineEnding = hasWindowsLineEndings ? '\r\n' : '\n';
    
    console.log(`📝 Detected line endings: ${hasWindowsLineEndings ? 'Windows (CRLF)' : 'Unix (LF)'}`);

    // Normalize to Unix line endings for processing
    content = content.replace(/\r\n/g, '\n');
    
    // Extract plugin name
    let pluginNameMatch = content.match(/const\s+pluginName\s*=\s*["']([^"']+)["']/);
    let pluginName = pluginNameMatch ? pluginNameMatch[1] : 'Unknown Plugin';

    // If no pluginName found, try to detect from filename
    if (!pluginNameMatch) {
        const fileName = path.basename(filePath, '.js');
        if (fileName.toLowerCase().includes('spectrum')) {
            pluginName = 'Spectrum Graph';
        } else if (fileName.toLowerCase().includes('scanner')) {
            pluginName = 'Scanner';
        } else {
            pluginName = fileName.replace(/[_-]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }
    }

    // Generate unique state variable name based on file path to avoid conflicts
    const fileHash = require('crypto').createHash('md5').update(filePath).digest('hex').substring(0, 8);
    const baseStateName = pluginName.replace(/\s+/g, '');
    const stateVarName = `${baseStateName}State_${fileHash}`;
    
    console.log(`📝 Plugin: ${pluginName}`);
    console.log(`🔧 State variable: ${stateVarName}`);

    // Step 1: Add state management
    const stateManagement = `
// Hot reload state management
if (!global.${stateVarName}) {
    global.${stateVarName} = {
        timers: new Set(),
        sockets: new Set(),
        originalHandleData: null,
        routeAdded: false,
        isInitialized: false,
        textSocket: null,
        extraSocket: null
    };
}
`;
    
    // Add state management at the very beginning after 'use strict'
    const useStrictPattern = /('use strict';?\s*\n)/;
    if (content.match(useStrictPattern)) {
        content = content.replace(useStrictPattern, `$1${stateManagement}\n`);
    } else {
        // Add at the very beginning after initial comments
        const afterCommentsPattern = /(^(?:\/\*[\s\S]*?\*\/\s*\n)*(?:\/\/.*\n)*)/;
        content = content.replace(afterCommentsPattern, `$1${stateManagement}\n`);
    }

    // Step 2: Check existing const variables
    const hasDebug = content.includes('const debug');
    const hasWebserverPort = content.includes('const webserverPort');
    const hasExternalWsUrl = content.includes('const externalWsUrl');
    
    console.log(`🔍 Existing const variables - debug: ${hasDebug}, webserverPort: ${hasWebserverPort}, externalWsUrl: ${hasExternalWsUrl}`);
    
    // Step 3: Add missing const variables and helper functions
    let helperSection = '';
    if (!hasDebug || !hasWebserverPort || !hasExternalWsUrl) {
        helperSection += '\n// const variables\n';
        if (!hasDebug) helperSection += 'const debug = false;\n';
        if (!hasWebserverPort) helperSection += 'const webserverPort = config.webserver.webserverPort || 8080;\n';
        if (!hasExternalWsUrl) helperSection += 'const externalWsUrl = `ws://127.0.0.1:${webserverPort}`;\n';
        helperSection += '\n';
    }
    
    helperSection += `// Tracked timer functions for hot reload
const originalSetTimeout = setTimeout;
const originalSetInterval = setInterval;

function trackedSetTimeout(callback, delay, ...args) {
    const timerId = originalSetTimeout((...callbackArgs) => {
        global.${stateVarName}.timers.delete(timerId);
        callback(...callbackArgs);
    }, delay, ...args);
    global.${stateVarName}.timers.add(timerId);
    return timerId;
}

function trackedSetInterval(callback, delay, ...args) {
    const timerId = originalSetInterval(callback, delay, ...args);
    global.${stateVarName}.timers.add(timerId);
    return timerId;
}
`;

    // Insert helper functions after state management
    content = content.replace(stateManagement, `${stateManagement}${helperSection}`);

    // Step 4: Detect WebSocket variables
    const webSocketVars = new Set();
    const webSocketPattern = /(?:(const|let|var)\s+)?(\w+)\s*=\s*new\s+WebSocket\s*\(/g;
    let match;
    while ((match = webSocketPattern.exec(content)) !== null) {
        webSocketVars.add(match[2]);
    }
    
    console.log(`🔍 Detected WebSocket variables: ${Array.from(webSocketVars).join(', ')}`);

    // Step 5: Convert timer calls
    content = content.replace(/setTimeout\s*\(/g, 'trackedSetTimeout(');
    content = content.replace(/setInterval\s*\(/g, 'trackedSetInterval(');

    // Step 6: Handle WebSocket imports and add WebSocket functions
    const webSocketImportPattern = /(const WebSocket = require\(['"]ws['"]\);)/;
    const webSocketImportMatch = content.match(webSocketImportPattern);
    
    console.log(`🔍 WebSocket import found: ${!!webSocketImportMatch}`);
    
    if (webSocketImportMatch) {
        const webSocketSection = `
const originalWebSocket = WebSocket;

function trackedWebSocket(url, protocols) {
    const socket = new originalWebSocket(url, protocols);
    global.${stateVarName}.sockets.add(socket);
    logInfo(\`Plugin: Created WebSocket to \${url}, total: \${global.${stateVarName}.sockets.size}\`);

    socket.addEventListener('close', () => {
        global.${stateVarName}.sockets.delete(socket);
        logInfo(\`Plugin: WebSocket closed, remaining: \${global.${stateVarName}.sockets.size}\`);
    });

    return socket;
}
`;
        content = content.replace(webSocketImportPattern, `$1${webSocketSection}`);
    }

    // Step 7: Convert WebSocket assignments - use simple string replacement to prevent double replacements
    webSocketVars.forEach(varName => {
        // Handle WebSocket creations - replace with trackedWebSocket
        content = content.replace(
            new RegExp(`const\\s+${varName}\\s*=\\s*new\\s+WebSocket\\s*\\(`, 'g'),
            `global.${stateVarName}.${varName} = trackedWebSocket(`
        );

        content = content.replace(
            new RegExp(`(let|var)\\s+${varName}\\s*=\\s*new\\s+WebSocket\\s*\\(`, 'g'),
            `global.${stateVarName}.${varName} = trackedWebSocket(`
        );

        // Handle simple assignments (but not if already converted)
        content = content.replace(
            new RegExp(`\\b${varName}\\s*=\\s*new\\s+WebSocket\\s*\\(`, 'g'),
            (match, offset) => {
                // Check if this line already has global state
                const lineStart = content.lastIndexOf('\n', offset) + 1;
                const lineEnd = content.indexOf('\n', offset);
                const line = content.substring(lineStart, lineEnd === -1 ? content.length : lineEnd);

                if (line.includes(`global.${stateVarName}.${varName}`)) {
                    return match; // Don't replace if already converted
                }
                return `global.${stateVarName}.${varName} = trackedWebSocket(`;
            }
        );
    });

    // Second pass: convert variable references
    webSocketVars.forEach(varName => {
        // Convert variable checks in conditions
        content = content.replace(
            new RegExp(`\\b${varName}(?=\\s*\\|\\||\\s*&&|\\s*\\?|\\s*===|\\s*!==|\\s*==|\\s*!=)`, 'g'),
            (match, offset) => {
                const lineStart = content.lastIndexOf('\n', offset) + 1;
                const lineEnd = content.indexOf('\n', offset);
                const line = content.substring(lineStart, lineEnd === -1 ? content.length : lineEnd);

                if (line.includes(`global.${stateVarName}.${varName}`)) {
                    return match;
                }
                return `global.${stateVarName}.${varName}`;
            }
        );

        // Convert readyState checks
        content = content.replace(
            new RegExp(`\\b${varName}\\.readyState`, 'g'),
            (match, offset) => {
                const lineStart = content.lastIndexOf('\n', offset) + 1;
                const lineEnd = content.indexOf('\n', offset);
                const line = content.substring(lineStart, lineEnd === -1 ? content.length : lineEnd);

                if (line.includes(`global.${stateVarName}.${varName}.readyState`)) {
                    return match;
                }
                return `global.${stateVarName}.${varName}.readyState`;
            }
        );

        // Convert method calls
        content = content.replace(
            new RegExp(`\\b${varName}\\.(send|close|addEventListener|removeEventListener)\\(`, 'g'),
            (match, method, offset) => {
                const lineStart = content.lastIndexOf('\n', offset) + 1;
                const lineEnd = content.indexOf('\n', offset);
                const line = content.substring(lineStart, lineEnd === -1 ? content.length : lineEnd);

                if (line.includes(`global.${stateVarName}.${varName}.${method}`)) {
                    return match;
                }
                return `global.${stateVarName}.${varName}.${method}(`;
            }
        );
    });

    // Step 8: Convert WebSocket creation calls
    content = content.replace(/new\s+WebSocket\s*\(/g, 'trackedWebSocket(');
    
    // Add local variable assignments after global WebSocket assignments
    webSocketVars.forEach(varName => {
        const globalAssignPattern = new RegExp(`(global\\.${stateVarName}\\.${varName}\\s*=\\s*trackedWebSocket\\([^)]+\\);)`, 'g');
        content = content.replace(globalAssignPattern, `$1\n            ${varName} = global.${stateVarName}.${varName};`);
    });

    // Step 9: Add cleanup function
    const webSocketCleanupLines = Array.from(webSocketVars).map(varName => 
        `    global.${stateVarName}.${varName} = null;`
    ).join('\n');
    
    const cleanupFunction = `
// Cleanup function for hot reload
function cleanup() {
    logInfo('Plugin: Cleaning up for hot reload...');

    // Clear all timers
    global.${stateVarName}.timers.forEach(timer => {
        clearTimeout(timer);
    });
    global.${stateVarName}.timers.clear();

    // Close all WebSocket connections
    logInfo(\`Plugin: Closing \${global.${stateVarName}.sockets.size} WebSocket connections...\`);
    global.${stateVarName}.sockets.forEach(socket => {
        if (socket && socket.readyState === 1) {
            socket.close(1000, 'Plugin reloading');
        }
    });
    global.${stateVarName}.sockets.clear();

    // Clear WebSocket variables
${webSocketCleanupLines}
    logInfo('Plugin: WebSocket cleanup completed');

    // Restore original dataHandler if it exists
    if (global.${stateVarName}.originalHandleData) {
        datahandlerReceived.handleData = global.${stateVarName}.originalHandleData;
        logInfo('Plugin: Restored original dataHandler');
    }

    global.${stateVarName}.isInitialized = false;
    logInfo('Plugin: Cleanup completed');
}

// Register cleanup with hot reload system
if (typeof __plugin !== 'undefined' && __plugin.registerCleanup) {
    __plugin.registerCleanup(cleanup);
}

// Prevent multiple initializations
if (global.${stateVarName}.isInitialized) {
    logInfo('Plugin: Already initialized, cleaning up previous instance...');
    cleanup();
}

global.${stateVarName}.isInitialized = true;
`;

    // Add cleanup function at the end of the file
    content += cleanupFunction;

    // Step 10: Convert WebSocket constants
    content = content.replace(/WebSocket\.OPEN/g, '1');
    content = content.replace(/WebSocket\.CLOSED/g, '3');
    content = content.replace(/WebSocket\.CONNECTING/g, '0');
    content = content.replace(/WebSocket\.CLOSING/g, '2');

    // Restore original line endings
    if (lineEnding === '\r\n') {
        content = content.replace(/\n/g, '\r\n');
    }

    // Generate hot reload filename
    const hotReloadPath = filePath.replace('_server.js', '_server_hot.js');

    // Validate that we're working with a server file
    if (!filePath.endsWith('_server.js')) {
        console.error('❌ Error: File must end with "_server.js" to convert to hot reload version');
        process.exit(1);
    }

    if (hotReloadPath === filePath) {
        console.error('❌ Error: Could not generate hot reload filename');
        process.exit(1);
    }

    // Save converted content to hot reload file, leave original untouched
    fs.writeFileSync(hotReloadPath, content);

    // Count conversions
    const timerMatches = (originalContent.match(/(setTimeout|setInterval)/g) || []).length;
    const socketMatches = (originalContent.match(/new\s+WebSocket/g) || []).length;

    console.log('✅ Conversion completed!');
    console.log(`📁 Original file: ${filePath} (unchanged)`);
    console.log(`🔥 Hot reload version created: ${hotReloadPath}`);
    console.log('');
    console.log('📊 Conversion Summary:');
    console.log(`   • ${timerMatches} timer calls converted`);
    console.log(`   • ${socketMatches} WebSocket creations converted`);
    console.log('   • Added global state management');
    console.log('   • Added cleanup function');
    console.log('   • Added initialization guards');
    console.log('');
    console.log('🎯 Hot Reload Priority System:');
    console.log('   • Server will now prioritize *_server_hot.js over *_server.js');
    console.log('   • Original plugin remains as fallback if hot reload version is removed');
    console.log('   • Both versions can coexist safely');
    console.log('');
    console.log('⚠️  Note: Generic conversion - only assumes FM-DX-Webserver framework functions');
    console.log('   Manual verification recommended for plugin-specific patterns');
}

// CLI usage
if (require.main === module) {
    const filePath = process.argv[2];
    if (!filePath) {
        console.log(`Usage: node ${path.basename(__filename)} <plugin-server-file>`);
        process.exit(1);
    }
    
    convertToHotReloadCompatible(filePath);
}

module.exports = { convertToHotReloadCompatible };
