
.setup-wrapper .form-group, .setup-wrapper textarea {
    display: inline-block;
    float: none;
}

.setup-wrapper .form-group {
    margin-right: 5px;
    margin-left: 5px;
}

.setup-wrapper h2 {
    font-size: 42px;
    font-weight: 300;
    padding: 20px 15px;
    text-align: left;
}

#wrapper textarea {
    width: 100%;
    max-width: 768px;
    background-color: var(--color-2);
    height: 100px;
    font-size: 14px;
    padding-top: 10px;
}

.sidenav {
    background-color: var(--color-main);
  }

  .sidenav li a:focus {
    outline: none;
  }

  .sidenav-content {
    flex: 1;
    position: relative;
    overflow-y: auto;
  }
  
  .sidenav .closebtn {
    position: absolute;
    top: 0;
    right: 25px;
    font-size: 36px;
    margin-left: 50px;
  }

  .sidenav h1 {
    font-size: 42px;
    text-transform: initial;
    font-weight: 300;
    text-align: center;
  }
ul.nav {
    list-style-type: none;
    padding: 15px 0;
    border-radius: 15px;
}

ul.nav li {
    padding: 12px 20px;
    cursor: pointer;
    transition: color 0.3s ease, background-color 0.3s ease;
    user-select: none;
}

ul.nav li a {
    color: var(--color-5) !important;
}


ul.nav li:hover {
    background-color: var(--color-3);
}

ul.nav li:hover a {
    color: var(--color-main) !important;
}

ul.nav li.active a {
    color: var(--color-main) !important;
    font-weight: bold;
}


li.active {
    background-color: var(--color-4);
}

.tab-content {
    display: none;
}

#navigation {
    position: fixed;
    top: 0;
    left: 0;
    width: 420px; /* Width of the sidenav */
    height: 100%;
    z-index: 1000; /* Ensure it's above other content */
    transition: margin-left 0.3s ease; /* Smooth transition */
  }
  
  .admin-wrapper {
    transition: margin-left 0.3s ease, width 0.3s ease;
  }

  .admin-wrapper > .panel-full > .panel-full {
    min-height: 100vh;
  }

#map {
    height:400px;
    width:100%;
    overflow: hidden;
    max-width:800px;
    margin: auto;
    margin-bottom: 20px;
}

.setup-wrapper h3 {
    font-weight: 300;
    margin: 8px;
    font-size: 36px;
    color: var(--color-5)
}

.setup-wrapper h4 {
    color: var(--color-4);
}


#console-output {
    background-color: #111;
    height: 300px;
    overflow-y:auto;
}
.w-250 {
    width: 250px !important
}

.w-200 {
    width: 200px !important
}

.w-150 {
    width: 150px !important
}

.w-100 {
    width: 100px !important;
}

@media only screen and (max-width: 768px) {
    .setup-wrapper .panel-33, .setup-wrapper .panel-50 {
        background: var(--color-1-transparent);
    }
    #navigation {
        width: 100vw; /* You can make the sidenav full width on mobile if you want */
      }
      
      .admin-wrapper {
        margin-left: 0;
        width: 100%;
      }
}

/* Plugin Management Styles */
.plugin-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 20px;
}

.plugin-card {
    background-color: var(--color-2);
    border: 1px solid var(--color-3);
    border-radius: 8px;
    padding: 16px 20px;
    transition: all 0.3s ease;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    min-height: 60px;
    overflow: hidden;
    gap: 20px;
}

.plugin-card:hover {
    border-color: var(--color-5);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Column 1: Plugin name, status, version */
.plugin-column-1 {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 140px;
    flex-shrink: 0;
}

/* Column for status indicator */
.plugin-column-status {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    min-width: 30px;
    flex-shrink: 0;
    padding-top: 2px;
}

.plugin-name {
    line-height: 10px;
    text-align: middle;
    margin: 0 0 4px 0;
    font-size: 18px;
    font-weight: 700;
    color: var(--color-main-bright);
}

.plugin-status, .plugin-version {
    line-height: 14px;
    text-align: middle;
    color: var(--color-4);
    font-size: 14px;
    margin: 2px 0;
}

.plugin-status strong, .plugin-version strong {
    color: var(--color-5);
}

/* Column 2: Load time and stats */
.plugin-column-2 {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
    min-width: 200px;
}

.plugin-load-time, .plugin-stats, .plugin-not-loaded {
    font-size: 14px;
    color: var(--color-4);
    margin: 2px 0;
    font-weight: 500;
}

.plugin-details {
    margin-top: 8px;
    font-size: 12px;
    color: var(--color-4);
}

.plugin-details div {
    margin-bottom: 4px;
}

.plugin-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
    align-items: flex-start;
    margin-top: 4px;
    min-width: 200px;
    justify-content: flex-end;
}

.plugin-actions .btn {
    min-width: 80px;
    font-size: 12px;
    padding: 6px 18px;
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.plugin-actions .btn i {
    margin-right: 4px;
    width: 12px;
    text-align: center;
}

.plugin-actions .btn.btn-disabled {
    background-color: #6c757d !important;
    color: #ffffff !important;
    opacity: 0.6;
    cursor: not-allowed;
}

.plugin-actions .btn.btn-disabled:hover {
    background-color: #6c757d !important;
}

/* Status Indicators */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-left: 8px;
    flex-shrink: 0;
    min-width: 12px;
    min-height: 12px;
}

.status-loaded {
    background-color: #28a745;
    box-shadow: 0 0 4px rgba(40, 167, 69, 0.5);
}

.status-unloaded {
    background-color: #6c757d;
}

.status-restarting {
    background-color: #ffc107;
    animation: pulse 1.5s infinite;
}

.status-error {
    background-color: #dc3545;
    box-shadow: 0 0 4px rgba(220, 53, 69, 0.5);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Status Legend */
.plugin-status-legend {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-top: 20px;
    padding: 0 4px;
}

.status-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: var(--color-2);
    border: 1px solid var(--color-3);
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.status-item:hover {
    border-color: var(--color-4);
    background-color: var(--color-2-5);
}

.status-item .status-indicator {
    margin-right: 12px;
    margin-left: 0;
    flex-shrink: 0;
}

/* Loading and Error States */
.loading-indicator {
    text-align: center;
    padding: 40px;
    color: var(--color-4);
    font-size: 16px;
}

.loading-indicator i {
    margin-right: 8px;
}

.plugin-error {
    text-align: center;
    padding: 40px;
    color: var(--color-danger);
    font-size: 16px;
    background-color: var(--color-2);
    border: 1px solid var(--color-danger);
    border-radius: 8px;
}

.plugin-error i {
    margin-right: 8px;
}

.no-plugins {
    text-align: center;
    padding: 40px;
    color: var(--color-4);
    font-size: 16px;
    background-color: var(--color-2);
    border: 1px solid var(--color-3);
    border-radius: 8px;
}

.no-plugins i {
    margin-right: 8px;
}

/* Responsive Design for Plugin Management */
@media (max-width: 1200px) {
    .plugin-actions {
        flex-wrap: wrap;
        gap: 6px;
    }

    .plugin-actions .btn {
        min-width: 70px;
        font-size: 11px;
    }
}

@media (max-width: 800px) {
    .plugin-card {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        min-height: auto;
        padding: 12px 16px;
    }

    .plugin-actions {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .plugin-card {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        min-height: auto;
        padding: 12px 16px;
    }

    .plugin-main-info {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .plugin-header {
        justify-content: space-between;
    }

    .plugin-name {
        min-width: auto;
        font-size: 15px;
    }

    .plugin-info {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .plugin-status, .plugin-version {
        white-space: normal;
        min-width: auto;
    }

    .plugin-details {
        flex-direction: column;
        gap: 4px;
    }

    .plugin-actions {
        justify-content: stretch;
        flex-wrap: nowrap;
    }

    .plugin-actions .btn {
        flex: 1;
        min-width: auto;
        font-size: 11px;
        padding: 8px 6px;
    }

    .plugin-status-legend {
        grid-template-columns: 1fr;
    }
}
