# Plugin Hot Reload System

This document explains how to use the new plugin hot reload system that allows you to restart server-side plugins without restarting the entire FM-DX Webserver.

## Features

- **Hot Reload**: Restart plugins without server downtime
- **Resource Cleanup**: Automatically cleans up timers, WebSocket connections, and event listeners
- **Fallback Support**: Falls back to traditional loading for incompatible plugins
- **Admin Interface**: Web-based management interface for plugin control
- **Real-time Status**: Live status updates and monitoring

## How It Works

The hot reload system works by:

1. **Wrapping Plugin Code**: Plugins are loaded in a controlled environment that tracks resources
2. **Resource Tracking**: All timers, WebSocket connections, and cleanup handlers are tracked
3. **Safe Cleanup**: When restarting, all tracked resources are properly cleaned up
4. **Module Cache Clearing**: Plugin modules are removed from Node.js require cache for fresh loading

## Using the Plugin Management Interface

### Accessing the Interface

1. Navigate to the server setup page: `http://your-server:port/setup`
2. Log in as an administrator
3. Click on "Plugin Management" in the sidebar

### Plugin Status Indicators

- **🟢 Loaded**: Plugin is running and can be hot-reloaded
- **⚫ Unloaded**: Plugin is not currently running
- **🟡 Restarting**: Plugin is being restarted (temporary status)
- **🔴 Error**: Plugin failed to load or restart

### Available Actions

- **Restart**: Unload and reload the plugin (preserves functionality)
- **Unload**: Stop the plugin completely
- **Load**: Start an unloaded plugin
- **Refresh**: Update the plugin list and status

## Plugin Compatibility

### Automatically Compatible

Most plugins work with hot reload out of the box, especially those that:
- Use standard timers (`setTimeout`, `setInterval`)
- Create WebSocket connections
- Don't manipulate Node.js internals

### Plugins That Need Modification

Some plugins may need minor modifications for optimal hot reload support:

#### Adding Cleanup Handlers

```javascript
// In your plugin code, register cleanup functions
if (typeof __plugin !== 'undefined' && __plugin.registerCleanup) {
    __plugin.registerCleanup(() => {
        // Your cleanup code here
        // Close database connections, stop custom timers, etc.
    });
}
```

#### Example Plugin Structure

```javascript
// plugin_example_server.js
'use strict';

const pluginName = "Example Plugin";

// Your plugin variables
let customInterval;
let wsConnection;

// Plugin initialization
function initPlugin() {
    // Set up timers (automatically tracked)
    customInterval = setInterval(() => {
        console.log('Plugin running...');
    }, 5000);
    
    // Set up WebSocket (automatically tracked if using global WebSocket)
    wsConnection = new WebSocket('ws://example.com');
    
    // Register custom cleanup if needed
    if (typeof __plugin !== 'undefined' && __plugin.registerCleanup) {
        __plugin.registerCleanup(() => {
            // Custom cleanup code
            if (customInterval) {
                clearInterval(customInterval);
            }
            if (wsConnection) {
                wsConnection.close();
            }
        });
    }
}

// Start the plugin
initPlugin();
```

## API Endpoints

The hot reload system provides REST API endpoints for programmatic control:

### Get All Plugins
```
GET /api/plugins
```
Returns list of all server-side plugins with their status.

### Get Plugin Status
```
GET /api/plugins/{pluginName}
```
Returns detailed status for a specific plugin.

### Restart Plugin
```
POST /api/plugins/{pluginName}/restart
```
Restarts the specified plugin.

### Load Plugin
```
POST /api/plugins/{pluginName}/load
```
Loads an unloaded plugin.

### Unload Plugin
```
POST /api/plugins/{pluginName}/unload
```
Unloads a running plugin.

## Troubleshooting

### Plugin Won't Restart

1. Check the server console for error messages
2. Verify the plugin file exists and has no syntax errors
3. Try unloading and loading the plugin instead of restarting
4. Check if the plugin has custom cleanup requirements

### Resource Leaks

If you notice resources not being cleaned up properly:

1. Add custom cleanup handlers to your plugin
2. Avoid using global variables that persist between reloads
3. Ensure all async operations are properly cancelled

### Fallback Mode

If a plugin can't be hot-reloaded, it will fall back to traditional loading:
- The plugin will still work normally
- Hot reload won't be available for that plugin
- Server restart will be required to reload the plugin

## Best Practices

1. **Test Hot Reload**: Always test plugin restart functionality during development
2. **Clean Code**: Write plugins that properly clean up after themselves
3. **Error Handling**: Include proper error handling in plugin code
4. **Resource Management**: Be mindful of timers, connections, and event listeners
5. **Logging**: Use proper logging to track plugin behavior during restarts

## Technical Details

### Files Modified/Added

- `server/plugin_manager.js` - Core plugin management system
- `server/plugin_wrapper.js` - Plugin wrapping and analysis utilities
- `server/endpoints.js` - Added hot reload API endpoints
- `server/index.js` - Updated plugin loading system
- `web/setup.ejs` - Added plugin management UI
- `web/js/plugin_management.js` - Frontend JavaScript for plugin management
- `web/css/setup.css` - Styling for plugin management interface

### Resource Tracking

The system tracks:
- `setTimeout` and `setInterval` timers
- WebSocket connections
- Custom cleanup handlers registered by plugins
- Module cache entries for proper cleanup

This ensures that when a plugin is restarted, all its resources are properly cleaned up to prevent memory leaks and duplicate functionality.
